# -*- coding: utf-8 -*-
"""
重构的主窗口 - 解决布局重叠问题
作者: Cursor AI
创建时间: 2025-07-05
编码标准: UTF-8无BOM
"""

import sys
from pathlib import Path
from typing import Optional, List, Dict, Any
from PyQt6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QSplitter,
    QMenuBar, QStatusBar, QToolBar, QMessageBox,
    QFileDialog, QProgressBar, QLabel, QScrollArea, QTabWidget
)
from PyQt6.QtCore import Qt, QTimer, pyqtSignal, QThread
from PyQt6.QtGui import QIcon, QPixmap, QKeySequence, QAction

from ..utils.logger import Logger
from ..utils.config_manager import ConfigManager
from .widgets.screenshot_widget import ScreenshotWidget
from .widgets.detection_panel_v2 import DetectionPanelV2
from .widgets.template_panel_v2 import TemplatePanelV2

from .widgets.config_panel_v2 import ConfigPanelV2
from .widgets.status_panel import StatusPanel


class MainWindowV2(QMainWindow):
    """重构的主窗口类 - 解决布局重叠问题"""
    
    # 信号定义
    detection_requested = pyqtSignal(dict)
    screenshot_requested = pyqtSignal(dict)
    
    def __init__(self, config_manager: ConfigManager):
        super().__init__()

        # 设置应用程序图标
        self.setup_application_icon()
        self.config_manager = config_manager
        self.logger = Logger()
        
        # 设置窗口属性
        self.setWindowTitle("YOLO OpenCV 界面检测工具 v2.0")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # 初始化组件
        self._init_ui()
        self._init_connections()
        self._apply_theme()
        


        self.logger.info("重构主窗口初始化完成")





    def _init_ui(self) -> None:
        """初始化用户界面"""
        # 创建菜单栏
        self._create_menu_bar()
        
        # 创建工具栏
        self._create_tool_bar()
        
        # 创建状态栏
        self._create_status_bar()
        
        # 创建中央控件
        self._create_central_widget()
    
    def _create_menu_bar(self) -> None:
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件(&F)")
        
        # 打开配置
        open_action = QAction("打开配置", self)
        open_action.setShortcut(QKeySequence.StandardKey.Open)
        open_action.triggered.connect(self._open_config)
        file_menu.addAction(open_action)
        
        # 保存配置
        save_action = QAction("保存配置", self)
        save_action.setShortcut(QKeySequence.StandardKey.Save)
        save_action.triggered.connect(self._save_config)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        # 退出
        exit_action = QAction("退出", self)
        exit_action.setShortcut(QKeySequence.StandardKey.Quit)
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具(&T)")
        
        # 重置布局
        reset_layout_action = QAction("重置布局", self)
        reset_layout_action.setShortcut(QKeySequence("Ctrl+R"))
        reset_layout_action.setStatusTip("重置界面布局到默认状态 (Ctrl+R)")
        reset_layout_action.triggered.connect(self._reset_layout)
        tools_menu.addAction(reset_layout_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu("帮助(&H)")

        # 用户手册
        user_manual_action = QAction("📖 用户手册", self)
        user_manual_action.setShortcut(QKeySequence("F1"))
        user_manual_action.setStatusTip("打开用户使用手册 (F1)")
        user_manual_action.triggered.connect(self._show_user_manual)
        help_menu.addAction(user_manual_action)

        help_menu.addSeparator()

        # 关于
        about_action = QAction("关于", self)
        about_action.triggered.connect(self._show_about)
        help_menu.addAction(about_action)
    
    def _create_tool_bar(self) -> None:
        """创建工具栏"""
        toolbar = self.addToolBar("主工具栏")
        toolbar.setMovable(False)
        
        # 开始检测
        self.start_action = QAction("开始检测", self)
        self.start_action.setToolTip("开始实时检测")
        self.start_action.triggered.connect(self._start_detection)
        toolbar.addAction(self.start_action)
        
        # 停止检测
        self.stop_action = QAction("停止检测", self)
        self.stop_action.setToolTip("停止实时检测")
        self.stop_action.setEnabled(False)
        self.stop_action.triggered.connect(self._stop_detection)
        toolbar.addAction(self.stop_action)
        
        toolbar.addSeparator()
        
        # 截图 - 统一的截图入口
        self.screenshot_action = QAction("📷 通用截图", self)
        self.screenshot_action.setToolTip(
            "📷 通用截图工具 (F12)\n\n"
            "🎯 功能说明：\n"
            "• 📸 全屏截图：捕获整个屏幕内容\n"
            "• ✂️ 区域选择：拖拽选择特定区域\n"
            "• 💾 图片保存：将截图保存到指定位置\n"
            "• 🏷️ 模板创建：从截图区域创建匹配模板\n"
            "• 🔍 数据提取：提取区域用于检测分析\n\n"
            "💡 适用场景：\n"
            "• 需要保存完整截图用于文档记录\n"
            "• 创建多种用途的模板和素材\n"
            "• 通用的屏幕内容捕获和处理\n\n"
            "⌨️ 快捷键：F12"
        )
        self.screenshot_action.setShortcut(QKeySequence("F12"))
        self.screenshot_action.triggered.connect(self._open_screenshot_tool)
        toolbar.addAction(self.screenshot_action)
        
        toolbar.addSeparator()
        
        # 配置
        config_action = QAction("配置", self)
        config_action.setToolTip("打开配置面板")
        config_action.triggered.connect(self._show_config)
        toolbar.addAction(config_action)

        toolbar.addSeparator()

        # 显示源代码
        source_code_action = QAction("📄 源代码", self)
        source_code_action.setToolTip(
            "显示完整的检测源代码\n"
            "包含截图、YOLO检测、坐标提取等功能\n"
            "可直接复制使用"
        )
        source_code_action.triggered.connect(self._show_source_code)
        toolbar.addAction(source_code_action)
    
    def _create_status_bar(self) -> None:
        """创建状态栏"""
        self.status_bar = self.statusBar()
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
        
        # 系统信息标签
        self.system_info_label = QLabel("CPU: 0% | 内存: 0MB")
        self.status_bar.addPermanentWidget(self.system_info_label)
        
        # 系统监控定时器
        self.system_timer = QTimer()
        self.system_timer.timeout.connect(self._update_system_info)
        self.system_timer.start(2000)  # 每2秒更新一次
    
    def _create_central_widget(self) -> None:
        """创建中央控件 - 重构的布局"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局 - 水平分割
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(5, 5, 5, 5)
        main_layout.setSpacing(5)
        
        # 创建主分割器
        main_splitter = QSplitter(Qt.Orientation.Horizontal)
        main_layout.addWidget(main_splitter)
        
        # 左侧面板 - 检测和模板
        left_panel = self._create_left_panel()
        main_splitter.addWidget(left_panel)
        
        # 中央面板 - 截图显示
        center_panel = self._create_center_panel()
        main_splitter.addWidget(center_panel)
        
        # 右侧面板 - 配置和状态
        right_panel = self._create_right_panel()
        main_splitter.addWidget(right_panel)
        
        # 设置分割器比例 - 左:中:右 = 1:2:1
        main_splitter.setSizes([300, 600, 300])
        main_splitter.setStretchFactor(0, 0)  # 左侧固定
        main_splitter.setStretchFactor(1, 1)  # 中央可伸缩
        main_splitter.setStretchFactor(2, 0)  # 右侧固定
    
    def _create_left_panel(self) -> QWidget:
        """创建左侧面板"""
        panel = QWidget()
        panel.setMinimumWidth(280)
        panel.setMaximumWidth(350)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建标签页控件
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 检测面板
        self.detection_panel = DetectionPanelV2(self.config_manager)
        scroll_area1 = QScrollArea()
        scroll_area1.setWidget(self.detection_panel)
        scroll_area1.setWidgetResizable(True)
        scroll_area1.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area1.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        tab_widget.addTab(scroll_area1, "🎯 检测")
        
        # 模板面板
        self.template_panel = TemplatePanelV2(self.config_manager)
        scroll_area2 = QScrollArea()
        scroll_area2.setWidget(self.template_panel)
        scroll_area2.setWidgetResizable(True)
        scroll_area2.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area2.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        tab_widget.addTab(scroll_area2, "📋 模板")
        
        return panel
    
    def _create_center_panel(self) -> QWidget:
        """创建中央面板"""
        panel = QWidget()
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 标题
        title_label = QLabel("📸 实时截图显示")
        title_label.setStyleSheet("""
            QLabel {
                font-size: 16px;
                font-weight: bold;
                color: #2c3e50;
                padding: 10px;
                background-color: #ecf0f1;
                border-radius: 5px;
                margin-bottom: 5px;
            }
        """)
        layout.addWidget(title_label)
        
        # 截图显示组件
        self.screenshot_widget = ScreenshotWidget()
        layout.addWidget(self.screenshot_widget)
        
        return panel
    
    def _create_right_panel(self) -> QWidget:
        """创建右侧面板"""
        panel = QWidget()
        panel.setMinimumWidth(280)
        panel.setMaximumWidth(350)
        
        layout = QVBoxLayout(panel)
        layout.setContentsMargins(5, 5, 5, 5)
        layout.setSpacing(5)
        
        # 创建标签页控件
        tab_widget = QTabWidget()
        layout.addWidget(tab_widget)
        
        # 配置面板
        self.config_panel = ConfigPanelV2(self.config_manager)
        scroll_area1 = QScrollArea()
        scroll_area1.setWidget(self.config_panel)
        scroll_area1.setWidgetResizable(True)
        scroll_area1.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area1.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        tab_widget.addTab(scroll_area1, "⚙️ 配置")


        
        # 状态面板
        self.status_panel = StatusPanel(self.config_manager)
        scroll_area3 = QScrollArea()
        scroll_area3.setWidget(self.status_panel)
        scroll_area3.setWidgetResizable(True)
        scroll_area3.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area3.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        tab_widget.addTab(scroll_area3, "📈 状态")
        
        return panel

    def _init_connections(self) -> None:
        """初始化信号连接"""
        # 检测面板信号
        if hasattr(self.detection_panel, 'detection_requested'):
            self.detection_panel.detection_requested.connect(self._handle_detection_request)
        if hasattr(self.detection_panel, 'screenshot_requested'):
            self.detection_panel.screenshot_requested.connect(self._handle_screenshot_request)

        # 模板面板信号
        if hasattr(self.template_panel, 'template_selected'):
            self.template_panel.template_selected.connect(self._handle_template_selected)

    def _apply_theme(self, theme: str = "default") -> None:
        """应用主题"""
        if theme == "default":
            self.setStyleSheet("""
                QMainWindow {
                    background-color: #f5f5f5;
                }
                QTabWidget::pane {
                    border: 1px solid #bdc3c7;
                    border-radius: 5px;
                    background-color: white;
                }
                QTabBar::tab {
                    background-color: #ecf0f1;
                    padding: 8px 12px;
                    margin-right: 2px;
                    border-top-left-radius: 5px;
                    border-top-right-radius: 5px;
                }
                QTabBar::tab:selected {
                    background-color: #3498db;
                    color: white;
                }
                QScrollArea {
                    border: none;
                    background-color: transparent;
                }
            """)

        self.logger.info(f"应用主题: {theme}")

    def _reset_layout(self) -> None:
        """重置布局到默认状态"""
        try:
            # 重新设置分割器比例 - 左:中:右 = 300:600:300
            if hasattr(self, 'main_splitter'):
                self.main_splitter.setSizes([300, 600, 300])

                # 确保分割器的拉伸因子正确设置
                self.main_splitter.setStretchFactor(0, 0)  # 左侧固定
                self.main_splitter.setStretchFactor(1, 1)  # 中央可伸缩
                self.main_splitter.setStretchFactor(2, 0)  # 右侧固定

                self.logger.info("界面布局已重置到默认状态")
                self.status_label.setText("✅ 布局已重置到默认状态")

                # 显示成功提示
                QMessageBox.information(
                    self,
                    "布局重置",
                    "界面布局已重置到默认状态\n\n"
                    "默认布局:\n"
                    "• 左侧面板: 300px (检测控制、模板管理)\n"
                    "• 中央面板: 600px (截图显示)\n"
                    "• 右侧面板: 300px (配置、状态)\n\n"
                    "您可以拖拽分割器调整布局，\n"
                    "随时使用 Ctrl+R 重置到默认状态。"
                )
            else:
                self.logger.warning("未找到主分割器，无法重置布局")
                self.status_label.setText("❌ 重置布局失败")

        except Exception as e:
            self.logger.error(f"重置布局失败: {e}")
            self.status_label.setText("❌ 重置布局失败")
            QMessageBox.warning(self, "错误", f"重置布局失败: {e}")

    def _update_system_info(self) -> None:
        """更新系统信息"""
        try:
            import psutil
            cpu_percent = psutil.cpu_percent()
            memory_info = psutil.virtual_memory()
            memory_mb = memory_info.used / 1024 / 1024

            self.system_info_label.setText(f"CPU: {cpu_percent:.1f}% | 内存: {memory_mb:.1f}MB")
        except ImportError:
            self.system_info_label.setText("CPU: N/A | 内存: N/A")
        except Exception as e:
            self.logger.error(f"更新系统信息失败: {e}")

    # 事件处理方法
    def _start_detection(self) -> None:
        """开始检测"""
        self.start_action.setEnabled(False)
        self.stop_action.setEnabled(True)
        self.status_label.setText("检测中...")
        self.progress_bar.setVisible(True)

        # 发送检测请求信号
        config = self.config_manager.get_detection_config()
        self.detection_requested.emit(config)

        self.logger.info("开始检测")

    def _stop_detection(self) -> None:
        """停止检测"""
        self.start_action.setEnabled(True)
        self.stop_action.setEnabled(False)
        self.status_label.setText("就绪")
        self.progress_bar.setVisible(False)

        self.logger.info("停止检测")

    def _open_screenshot_tool(self) -> None:
        """打开统一的截图工具"""
        try:
            self.status_label.setText("正在启动截图工具...")

            # 使用统一的截图预览对话框
            from ..utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()

            if screenshot_helper.is_available():
                # 先截取全屏作为基础
                image, pixmap, filepath = screenshot_helper.take_screenshot_with_pixmap(save_to_file=True)

                if image is not None and pixmap is not None and not pixmap.isNull():
                    # 打开统一的截图预览对话框
                    from .screenshot_preview_dialog import ScreenshotPreviewDialog

                    dialog = ScreenshotPreviewDialog(image, filepath, self)
                    dialog.screenshot_saved.connect(self._on_screenshot_saved)
                    dialog.template_created.connect(self._on_template_created)
                    dialog.region_extracted.connect(self._on_region_extracted)

                    # 显示对话框
                    result = dialog.exec()
                    if result == ScreenshotPreviewDialog.DialogCode.Accepted:
                        self.logger.info("截图工具操作完成")
                    else:
                        self.status_label.setText("截图工具已关闭")

                    # 显示截图到中央面板
                    if hasattr(self, 'screenshot_widget'):
                        self.screenshot_widget.set_screenshot(pixmap, filepath)

                else:
                    self.status_label.setText("截图失败，请重试")
                    self.logger.error("截图失败")
            else:
                self.status_label.setText("截图服务不可用")
                self.logger.error("截图服务不可用")

        except Exception as e:
            self.status_label.setText(f"截图工具启动失败: {e}")
            self.logger.error(f"打开截图工具失败: {e}")

    def _on_screenshot_saved(self, filepath):
        """处理截图保存事件"""
        try:
            from pathlib import Path
            filename = Path(filepath).name
            self.status_label.setText(f"截图已保存: {filename}")
            self.logger.info(f"截图已保存: {filepath}")
        except Exception as e:
            self.logger.error(f"处理截图保存事件失败: {e}")

    def _on_template_created(self, template_data):
        """处理模板创建事件"""
        try:
            template_name = template_data.get('name', 'Unknown')
            self.status_label.setText(f"模板已创建: {template_name}")
            self.logger.info(f"模板已创建: {template_name}")

            # 通知模板面板添加新模板
            if hasattr(self, 'template_panel') and self.template_panel:
                # 直接调用模板面板的模板创建处理方法
                self.template_panel._on_template_captured(template_data)
                self.logger.info(f"模板已传递给模板面板: {template_name}")

        except Exception as e:
            self.logger.error(f"处理模板创建事件失败: {e}")

    def _on_region_extracted(self, image, region):
        """处理区域提取事件"""
        try:
            x, y, width, height = region
            self.status_label.setText(f"区域已提取: {width}×{height}")
            self.logger.info(f"区域已提取: ({x}, {y}, {width}, {height})")

            # 显示提取的区域到中央面板
            from ..utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            pixmap = screenshot_helper.numpy_to_qpixmap(image)

            if pixmap and not pixmap.isNull() and hasattr(self, 'screenshot_widget'):
                self.screenshot_widget.set_screenshot(pixmap)

        except Exception as e:
            self.logger.error(f"处理区域提取事件失败: {e}")

    def _on_screenshot_taken(self, image, filepath):
        """处理截图完成事件"""
        try:
            # 转换为QPixmap
            from ..utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            pixmap = screenshot_helper.numpy_to_qpixmap(image)

            if pixmap:
                # 显示截图
                if hasattr(self, 'screenshot_widget'):
                    self.screenshot_widget.set_screenshot(pixmap)

                self.status_label.setText(f"截图已保存: {filepath}")
                self.logger.info(f"截图已保存: {filepath}")

                # 发送截图信号
                config = {'screenshot_image': image, 'screenshot_pixmap': pixmap, 'screenshot_path': filepath}
                self.screenshot_requested.emit(config)
            else:
                self.status_label.setText("图像转换失败")
                self.logger.error("图像转换失败")

        except Exception as e:
            self.status_label.setText(f"处理截图失败: {e}")
            self.logger.error(f"处理截图失败: {e}")

    def _on_region_selected(self, image, region):
        """处理区域选择事件"""
        try:
            x, y, width, height = region

            # 转换为QPixmap
            from ..utils.screenshot_helper import get_screenshot_helper
            screenshot_helper = get_screenshot_helper()
            pixmap = screenshot_helper.numpy_to_qpixmap(image)

            if pixmap:
                # 显示区域图像
                if hasattr(self, 'screenshot_widget'):
                    self.screenshot_widget.set_screenshot(pixmap)

                self.status_label.setText(f"区域选择完成: {width}×{height}")
                self.logger.info(f"区域选择完成: {width}×{height}")

                # 发送区域选择信号
                config = {'region_image': image, 'region_pixmap': pixmap, 'region': region}
                self.screenshot_requested.emit(config)
            else:
                self.status_label.setText("区域图像转换失败")
                self.logger.error("区域图像转换失败")

        except Exception as e:
            self.status_label.setText(f"处理区域选择失败: {e}")
            self.logger.error(f"处理区域选择失败: {e}")

    def _show_config(self) -> None:
        """显示配置面板"""
        # 切换到配置标签页
        if hasattr(self, 'right_tab_widget'):
            self.right_tab_widget.setCurrentIndex(0)

    def _open_config(self) -> None:
        """打开配置文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开配置文件", "", "YAML文件 (*.yaml *.yml);;所有文件 (*)"
        )
        if file_path:
            try:
                self.config_manager.load_config(file_path)
                self.status_label.setText(f"已加载配置: {Path(file_path).name}")
                self.logger.info(f"加载配置文件: {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"加载配置文件失败: {e}")
                self.logger.error(f"加载配置文件失败: {e}")

    def _save_config(self) -> None:
        """保存配置文件"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存配置文件", "", "YAML文件 (*.yaml *.yml);;所有文件 (*)"
        )
        if file_path:
            try:
                self.config_manager.save_config(file_path)
                self.status_label.setText(f"已保存配置: {Path(file_path).name}")
                self.logger.info(f"保存配置文件: {file_path}")
            except Exception as e:
                QMessageBox.warning(self, "错误", f"保存配置文件失败: {e}")
                self.logger.error(f"保存配置文件失败: {e}")

    def _show_user_manual(self) -> None:
        """显示用户使用手册"""
        try:
            from .dialogs.user_manual_dialog import UserManualDialog
            dialog = UserManualDialog(self)
            dialog.exec()
        except Exception as e:
            self.logger.error(f"显示用户手册失败: {e}")
            # 如果对话框创建失败，使用简单的消息框显示基本信息
            QMessageBox.information(
                self,
                "用户手册",
                "📖 YOLO OpenCV检测器用户手册\n\n"
                "详细的用户手册文档位于项目根目录下的\"用户使用手册.md\"文件中。\n\n"
                "主要内容包括：\n"
                "• 工具简介和功能特点\n"
                "• 安装和环境配置\n"
                "• GUI界面详细说明\n"
                "• 检测模式使用方法\n"
                "• 源代码窗口和使用示例\n"
                "• 常见问题解答\n"
                "• 配置参数说明\n\n"
                "如需查看完整手册，请打开项目目录下的\"用户使用手册.md\"文件。"
            )

    def _show_about(self) -> None:
        """显示关于对话框"""
        QMessageBox.about(
            self,
            "关于",
            "YOLO OpenCV 界面检测工具 v2.0\n\n"
            "重构版本 - 解决布局重叠问题\n"
            "基于PyQt6和OpenCV开发\n\n"
            "联系方式: <EMAIL>\n"
            "创建时间: 2025-07-05\n\n"
            "功能特性:\n"
            "• YOLO目标检测\n"
            "• 模板匹配检测\n"
            "• 智能去重机制\n"
            "• 实时屏幕监控\n"
            "• 用户友好界面"
        )

    def _handle_detection_request(self, config: dict) -> None:
        """处理检测请求"""
        self.logger.info(f"处理检测请求: {config}")
        self.detection_requested.emit(config)

    def _handle_screenshot_request(self, config: dict) -> None:
        """处理截图请求"""
        try:
            action = config.get('action', 'unknown')
            self.logger.info(f"处理截图请求: {action}")

            # 处理不同类型的截图请求
            if action == 'screenshot_taken' and 'screenshot_pixmap' in config:
                # 全屏截图
                pixmap = config['screenshot_pixmap']
                filepath = config.get('screenshot_path', '')

                if pixmap and not pixmap.isNull():
                    # 显示截图到中央面板
                    if hasattr(self, 'screenshot_widget'):
                        self.screenshot_widget.set_screenshot(pixmap, filepath)
                        self.logger.info("截图已显示到中央面板")

                    # 更新状态栏
                    if filepath:
                        from pathlib import Path
                        filename = Path(filepath).name
                        self.status_label.setText(f"截图已保存: {filename}")
                    else:
                        self.status_label.setText("截图完成")

            elif action == 'region_extracted' and 'region_pixmap' in config:
                # 区域提取
                pixmap = config['region_pixmap']
                region = config.get('region', (0, 0, 0, 0))

                if pixmap and not pixmap.isNull():
                    # 显示区域图像到中央面板
                    if hasattr(self, 'screenshot_widget'):
                        self.screenshot_widget.set_screenshot(pixmap)
                        self.logger.info("区域图像已显示到中央面板")

                    # 更新状态栏
                    x, y, width, height = region
                    self.status_label.setText(f"区域已提取: {width}×{height}")

            elif action == 'realtime_detection' and 'screenshot_pixmap' in config:
                # 实时检测截图
                pixmap = config['screenshot_pixmap']
                detections = config.get('detections', [])
                screenshot_image = config.get('screenshot_image')

                if pixmap and not pixmap.isNull():
                    # 显示实时截图到中央面板
                    if hasattr(self, 'screenshot_widget'):
                        self.screenshot_widget.set_screenshot(pixmap)

                        # 如果有检测结果，显示检测结果
                        if detections and hasattr(self.screenshot_widget, 'set_detection_results'):
                            self.screenshot_widget.set_detection_results(detections)

                        self.logger.debug(f"实时检测截图已显示，检测到{len(detections)}个目标")





            elif action == 'template_created':
                # 模板创建 - 来自检测面板的模板创建信号
                template_data = config.get('template_data', {})
                template_name = template_data.get('name', 'Unknown')
                self.status_label.setText(f"模板已创建: {template_name}")

                # 传递给模板面板
                if hasattr(self, 'template_panel') and self.template_panel:
                    self.template_panel._on_template_captured(template_data)
                    self.logger.info(f"检测面板创建的模板已传递给模板面板: {template_name}")

            # 发送截图信号给其他组件
            self.screenshot_requested.emit(config)

        except Exception as e:
            self.logger.error(f"处理截图请求失败: {e}")
            self.status_label.setText(f"处理截图失败: {str(e)[:30]}")

    def _handle_template_selected(self, template_data: dict) -> None:
        """处理模板选择"""
        self.logger.info(f"选择模板: {template_data.get('name', 'Unknown')}")
        self.status_label.setText(f"已选择模板: {template_data.get('name', 'Unknown')}")



    def _show_source_code(self) -> None:
        """显示检测源代码"""
        try:
            from .dialogs.source_code_dialog import SourceCodeDialog

            # 创建对话框实例
            dialog = SourceCodeDialog(self)

            # 显示对话框
            dialog.exec()

        except Exception as e:
            self.logger.error(f"显示源代码失败: {e}")
            import traceback
            traceback.print_exc()

            # 尝试使用简化版本作为备用
            try:
                from .dialogs.simple_source_dialog import SimpleSourceCodeDialog
                print("🔄 原始对话框失败，使用简化版...")

                dialog = SimpleSourceCodeDialog(self)
                dialog.exec()

            except Exception as e2:
                self.logger.error(f"简化版源代码对话框也失败: {e2}")

                # 最后的备用方案
                from PyQt6.QtWidgets import QMessageBox
                QMessageBox.critical(
                    self,
                    "源代码对话框错误",
                    f"源代码对话框创建失败:\n{str(e)}\n\n请查看控制台输出获取详细错误信息。"
                )

    def closeEvent(self, event) -> None:
        """窗口关闭事件"""
        self.logger.info("重构主窗口关闭")
        event.accept()


    def setup_application_icon(self):
        """设置应用程序图标"""
        try:
            from PyQt6.QtGui import QIcon
            from PyQt6.QtWidgets import QApplication

            # 图标文件路径
            icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_app.ico"
            taskbar_icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_taskbar.ico"

            # 优先使用专门的任务栏图标
            if taskbar_icon_path.exists():
                icon = QIcon(str(taskbar_icon_path))
                print(f"✅ 使用任务栏专用图标: {taskbar_icon_path}")
            elif icon_path.exists():
                icon = QIcon(str(icon_path))
                print(f"✅ 使用应用程序图标: {icon_path}")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")
                return

            # 设置窗口图标
            self.setWindowIcon(icon)

            # 设置应用程序图标（任务栏）
            app = QApplication.instance()
            if app:
                app.setWindowIcon(icon)
                print("✅ 任务栏图标已设置")

            # 确保图标在最小化时也显示
            self.setWindowFlags(self.windowFlags() | Qt.WindowType.WindowMinimizeButtonHint)

            print("✅ 应用程序图标设置完成")

        except Exception as e:
            print(f"❌ 设置应用程序图标失败: {e}")