@echo off
chcp 65001 >nul
title 2025.7.27 - YOLO Detection Script

echo ========================================
echo YOLO Detection Standalone Script
echo ========================================
echo.

REM Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not installed or not in PATH
    echo Please install Python 3.7+ and try again
    pause
    exit /b 1
)

REM Check if virtual environment should be used
if exist "venv\Scripts\activate.bat" (
    echo Activating virtual environment...
    call venv\Scripts\activate.bat
)

REM Check if requirements are installed
echo Checking dependencies...
python -c "import cv2, numpy, PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    python -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)

echo.
echo Starting detection script...
echo.

REM Run the main script
python main.py

REM Keep window open
echo.
echo Script execution completed.
pause
