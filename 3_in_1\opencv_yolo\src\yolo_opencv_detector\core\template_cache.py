#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板缓存管理

提供高效的模板图像缓存机制，减少重复加载，提升性能。

Created: 2025-07-27
Author: Augment Agent
"""

import time
import threading
from typing import Dict, Optional, Any, Tuple
from collections import OrderedDict
import numpy as np
from pathlib import Path

from .template_models import Template
from .template_exceptions import TemplateCacheError, handle_template_errors


class LRUCache:
    """LRU缓存实现"""
    
    def __init__(self, max_size: int = 100):
        self.max_size = max_size
        self.cache: OrderedDict = OrderedDict()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存项"""
        with self.lock:
            if key in self.cache:
                # 移动到末尾（最近使用）
                value = self.cache.pop(key)
                self.cache[key] = value
                return value
            return None
    
    def put(self, key: str, value: Any) -> None:
        """添加缓存项"""
        with self.lock:
            if key in self.cache:
                # 更新现有项
                self.cache.pop(key)
            elif len(self.cache) >= self.max_size:
                # 移除最久未使用的项
                self.cache.popitem(last=False)
            
            self.cache[key] = value
    
    def remove(self, key: str) -> bool:
        """移除缓存项"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def size(self) -> int:
        """获取缓存大小"""
        with self.lock:
            return len(self.cache)
    
    def keys(self) -> list:
        """获取所有键"""
        with self.lock:
            return list(self.cache.keys())


class TemplateImageCache:
    """模板图像缓存"""
    
    def __init__(self, max_size: int = 50, max_memory_mb: int = 100):
        self.max_size = max_size
        self.max_memory_bytes = max_memory_mb * 1024 * 1024
        self.cache = LRUCache(max_size)
        self.memory_usage = 0
        self.access_times: Dict[str, float] = {}
        self.lock = threading.RLock()
    
    @handle_template_errors
    def get_image(self, template_id: str, file_path: Optional[Path] = None) -> Optional[np.ndarray]:
        """
        获取模板图像
        
        Args:
            template_id: 模板ID
            file_path: 文件路径（用于加载）
            
        Returns:
            图像数组或None
        """
        with self.lock:
            # 尝试从缓存获取
            cached_item = self.cache.get(template_id)
            if cached_item is not None:
                self.access_times[template_id] = time.time()
                return cached_item['image']
            
            # 如果缓存中没有，尝试加载
            if file_path and file_path.exists():
                image = self._load_image(file_path)
                if image is not None:
                    self.put_image(template_id, image, file_path)
                    return image
            
            return None
    
    @handle_template_errors
    def put_image(self, template_id: str, image: np.ndarray, file_path: Path) -> None:
        """
        缓存模板图像
        
        Args:
            template_id: 模板ID
            image: 图像数组
            file_path: 文件路径
        """
        with self.lock:
            # 计算图像内存大小
            image_size = image.nbytes
            
            # 检查内存限制
            if image_size > self.max_memory_bytes:
                raise TemplateCacheError(
                    template_id, 
                    "put", 
                    f"图像过大: {image_size} 字节，超过限制 {self.max_memory_bytes} 字节"
                )
            
            # 确保有足够内存空间
            self._ensure_memory_space(image_size)
            
            # 缓存图像
            cache_item = {
                'image': image,
                'file_path': str(file_path),
                'size': image_size,
                'cached_time': time.time()
            }
            
            self.cache.put(template_id, cache_item)
            self.memory_usage += image_size
            self.access_times[template_id] = time.time()
    
    def remove_image(self, template_id: str) -> bool:
        """移除缓存的图像"""
        with self.lock:
            cached_item = self.cache.get(template_id)
            if cached_item:
                self.memory_usage -= cached_item['size']
                self.cache.remove(template_id)
                if template_id in self.access_times:
                    del self.access_times[template_id]
                return True
            return False
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
            self.memory_usage = 0
            self.access_times.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            return {
                'cache_size': self.cache.size(),
                'max_size': self.max_size,
                'memory_usage_mb': self.memory_usage / (1024 * 1024),
                'max_memory_mb': self.max_memory_bytes / (1024 * 1024),
                'memory_usage_percent': (self.memory_usage / self.max_memory_bytes) * 100,
                'cached_templates': list(self.cache.keys())
            }
    
    def _ensure_memory_space(self, required_size: int) -> None:
        """确保有足够的内存空间"""
        while (self.memory_usage + required_size) > self.max_memory_bytes and self.cache.size() > 0:
            # 找到最久未访问的项
            oldest_key = min(self.access_times.keys(), key=lambda k: self.access_times[k])
            self.remove_image(oldest_key)
    
    def _load_image(self, file_path: Path) -> Optional[np.ndarray]:
        """加载图像文件"""
        try:
            import cv2
            
            # 使用支持中文路径的方法
            image_data = np.fromfile(str(file_path), dtype=np.uint8)
            image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            
            if image is None:
                raise TemplateCacheError(
                    str(file_path),
                    "load",
                    f"无法解码图像文件: {file_path}"
                )
            
            return image
            
        except Exception as e:
            raise TemplateCacheError(
                str(file_path),
                "load",
                f"加载图像失败: {e}"
            )


class TemplateMetadataCache:
    """模板元数据缓存"""
    
    def __init__(self, max_size: int = 200):
        self.cache = LRUCache(max_size)
        self.lock = threading.RLock()
    
    def get_template(self, template_id: str) -> Optional[Template]:
        """获取模板对象"""
        with self.lock:
            return self.cache.get(template_id)
    
    def put_template(self, template: Template) -> None:
        """缓存模板对象"""
        with self.lock:
            self.cache.put(template.id, template)
    
    def remove_template(self, template_id: str) -> bool:
        """移除模板缓存"""
        with self.lock:
            return self.cache.remove(template_id)
    
    def clear(self) -> None:
        """清空缓存"""
        with self.lock:
            self.cache.clear()
    
    def get_all_templates(self) -> Dict[str, Template]:
        """获取所有缓存的模板"""
        with self.lock:
            result = {}
            for key in self.cache.keys():
                template = self.cache.get(key)
                if template:
                    result[key] = template
            return result


class TemplateCache:
    """统一的模板缓存管理器"""
    
    def __init__(self, image_cache_size: int = 50, metadata_cache_size: int = 200, max_memory_mb: int = 100):
        self.image_cache = TemplateImageCache(image_cache_size, max_memory_mb)
        self.metadata_cache = TemplateMetadataCache(metadata_cache_size)
        self.lock = threading.RLock()
    
    def get_template_with_image(self, template_id: str) -> Optional[Tuple[Template, np.ndarray]]:
        """获取模板及其图像"""
        with self.lock:
            template = self.metadata_cache.get_template(template_id)
            if template:
                image = self.image_cache.get_image(template_id, template.file_path)
                if image is not None:
                    return template, image
            return None
    
    def cache_template(self, template: Template, image: Optional[np.ndarray] = None) -> None:
        """缓存模板和图像"""
        with self.lock:
            # 缓存模板元数据
            self.metadata_cache.put_template(template)
            
            # 缓存图像（如果提供）
            if image is not None:
                self.image_cache.put_image(template.id, image, template.file_path)
    
    def remove_template(self, template_id: str) -> None:
        """移除模板缓存"""
        with self.lock:
            self.metadata_cache.remove_template(template_id)
            self.image_cache.remove_image(template_id)
    
    def clear_all(self) -> None:
        """清空所有缓存"""
        with self.lock:
            self.metadata_cache.clear()
            self.image_cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self.lock:
            image_stats = self.image_cache.get_stats()
            return {
                'image_cache': image_stats,
                'metadata_cache_size': self.metadata_cache.cache.size(),
                'total_cached_templates': len(set(
                    list(self.metadata_cache.cache.keys()) + 
                    list(self.image_cache.cache.keys())
                ))
            }
