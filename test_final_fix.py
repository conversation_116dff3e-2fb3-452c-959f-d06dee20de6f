#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终修复的GUI状态捕获功能
"""

from datetime import datetime

def test_final_static_code():
    """测试最终修复的静态快照代码"""
    try:
        print("🧪 测试最终修复的静态快照代码...")
        
        # 模拟GUI状态
        gui_state = {
            'confidence_threshold': 0.75,
            'nms_threshold': 0.45,
            'selected_template': '测试模板',
            'detection_interval': 2.0,
            'export_timestamp': datetime.now().isoformat(),
            'auto_detect': True
        }
        
        # 生成修复后的静态快照代码
        template_info = f"'{gui_state['selected_template']}'" if gui_state['selected_template'] else "None"
        
        static_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（修复版本）
导出时间: {gui_state['export_timestamp']}
"""

from datetime import datetime

# 导出时刻的固化参数
EXPORTED_CONFIDENCE_THRESHOLD = {gui_state['confidence_threshold']:.3f}
EXPORTED_NMS_THRESHOLD = {gui_state['nms_threshold']:.3f}
EXPORTED_TEMPLATE_NAME = {template_info}
EXPORTED_DETECTION_INTERVAL = {gui_state['detection_interval']:.1f}
EXPORTED_AUTO_DETECT = {gui_state['auto_detect']}
EXPORTED_TIMESTAMP = "{gui_state['export_timestamp']}"

def main():
    """主函数"""
    print("🎯 YOLO检测器 - 静态快照版本（修复版）")
    print("📅 导出时间: " + EXPORTED_TIMESTAMP)
    print("⚙️  固化参数:")
    print("   • 置信度阈值: " + str(EXPORTED_CONFIDENCE_THRESHOLD))
    print("   • NMS阈值: " + str(EXPORTED_NMS_THRESHOLD))
    print("   • 选择模板: " + str(EXPORTED_TEMPLATE_NAME))
    print("   • 检测间隔: " + str(EXPORTED_DETECTION_INTERVAL) + "秒")
    print("   • 自动检测: " + str(EXPORTED_AUTO_DETECT))
    print("\\n✅ 静态快照代码运行成功！")

if __name__ == "__main__":
    main()
'''
        
        print("✅ 修复后的静态快照代码生成成功")
        print(f"📝 代码长度: {len(static_code)} 字符")
        
        # 执行测试代码
        print("🚀 执行测试代码...")
        exec(static_code)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_final_static_code()
    if success:
        print("\n🎉 最终修复版本测试通过！")
    else:
        print("\n💥 最终修复版本测试失败！")
