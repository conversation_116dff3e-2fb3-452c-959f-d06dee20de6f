#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板验证器

提供模板数据验证功能，确保模板数据的完整性和有效性。

Created: 2025-07-27
Author: Augment Agent
"""

import re
from pathlib import Path
from typing import List, Optional, Dict, Any
import numpy as np

from .template_models import Template, TemplateCategory, TemplateStatus
from .template_exceptions import TemplateValidationError


class TemplateValidator:
    """模板验证器"""
    
    # 支持的图像格式
    SUPPORTED_IMAGE_FORMATS = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif'}
    
    # 模板名称规则
    NAME_PATTERN = re.compile(r'^[a-zA-Z0-9\u4e00-\u9fa5_\-\s]+$')
    MIN_NAME_LENGTH = 1
    MAX_NAME_LENGTH = 100
    
    # 图像尺寸限制
    MIN_IMAGE_SIZE = (1, 1)
    MAX_IMAGE_SIZE = (4096, 4096)
    
    # 文件大小限制（字节）
    MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB
    
    @classmethod
    def validate_template(cls, template: Template) -> List[str]:
        """
        验证模板对象
        
        Args:
            template: 要验证的模板对象
            
        Returns:
            验证错误列表，空列表表示验证通过
        """
        errors = []
        
        # 验证基本信息
        errors.extend(cls._validate_basic_info(template))
        
        # 验证文件信息
        errors.extend(cls._validate_file_info(template))
        
        # 验证检测参数
        errors.extend(cls._validate_detection_params(template))
        
        return errors
    
    @classmethod
    def validate_template_data(cls, data: Dict[str, Any]) -> List[str]:
        """
        验证模板数据字典
        
        Args:
            data: 模板数据字典
            
        Returns:
            验证错误列表
        """
        errors = []
        
        # 验证必需字段
        required_fields = ['name']
        for field in required_fields:
            if field not in data or not data[field]:
                errors.append(f"缺少必需字段: {field}")
        
        # 验证名称
        if 'name' in data:
            name_errors = cls._validate_name(data['name'])
            errors.extend(name_errors)
        
        # 验证分类
        if 'category' in data:
            category_errors = cls._validate_category(data['category'])
            errors.extend(category_errors)
        
        # 验证状态
        if 'status' in data:
            status_errors = cls._validate_status(data['status'])
            errors.extend(status_errors)
        
        # 验证阈值
        if 'threshold' in data:
            threshold_errors = cls._validate_threshold(data['threshold'])
            errors.extend(threshold_errors)
        
        return errors
    
    @classmethod
    def validate_image_file(cls, file_path: Path) -> List[str]:
        """
        验证图像文件
        
        Args:
            file_path: 图像文件路径
            
        Returns:
            验证错误列表
        """
        errors = []
        
        # 检查文件是否存在
        if not file_path.exists():
            errors.append(f"文件不存在: {file_path}")
            return errors
        
        # 检查文件格式
        if file_path.suffix.lower() not in cls.SUPPORTED_IMAGE_FORMATS:
            errors.append(f"不支持的图像格式: {file_path.suffix}")
        
        # 检查文件大小
        try:
            file_size = file_path.stat().st_size
            if file_size > cls.MAX_FILE_SIZE:
                errors.append(f"文件过大: {file_size} 字节 (最大: {cls.MAX_FILE_SIZE} 字节)")
            elif file_size == 0:
                errors.append("文件为空")
        except Exception as e:
            errors.append(f"无法获取文件信息: {e}")
        
        # 尝试加载图像验证格式
        try:
            import cv2
            image = cv2.imread(str(file_path))
            if image is None:
                errors.append("无法读取图像文件，可能格式损坏")
            else:
                # 验证图像尺寸
                height, width = image.shape[:2]
                if width < cls.MIN_IMAGE_SIZE[0] or height < cls.MIN_IMAGE_SIZE[1]:
                    errors.append(f"图像尺寸过小: {width}x{height} (最小: {cls.MIN_IMAGE_SIZE[0]}x{cls.MIN_IMAGE_SIZE[1]})")
                elif width > cls.MAX_IMAGE_SIZE[0] or height > cls.MAX_IMAGE_SIZE[1]:
                    errors.append(f"图像尺寸过大: {width}x{height} (最大: {cls.MAX_IMAGE_SIZE[0]}x{cls.MAX_IMAGE_SIZE[1]})")
        except ImportError:
            errors.append("OpenCV未安装，无法验证图像格式")
        except Exception as e:
            errors.append(f"图像验证失败: {e}")
        
        return errors
    
    @classmethod
    def _validate_basic_info(cls, template: Template) -> List[str]:
        """验证基本信息"""
        errors = []
        
        # 验证ID
        if not template.id:
            errors.append("模板ID不能为空")
        
        # 验证名称
        name_errors = cls._validate_name(template.name)
        errors.extend(name_errors)
        
        # 验证描述长度
        if len(template.description) > 500:
            errors.append("描述过长（最大500字符）")
        
        return errors
    
    @classmethod
    def _validate_file_info(cls, template: Template) -> List[str]:
        """验证文件信息"""
        errors = []
        
        if template.file_path:
            file_errors = cls.validate_image_file(template.file_path)
            errors.extend(file_errors)
        
        return errors
    
    @classmethod
    def _validate_detection_params(cls, template: Template) -> List[str]:
        """验证检测参数"""
        errors = []
        
        # 验证阈值
        threshold_errors = cls._validate_threshold(template.threshold)
        errors.extend(threshold_errors)
        
        # 验证方法
        valid_methods = {
            'TM_CCOEFF', 'TM_CCOEFF_NORMED',
            'TM_CCORR', 'TM_CCORR_NORMED',
            'TM_SQDIFF', 'TM_SQDIFF_NORMED'
        }
        if template.method not in valid_methods:
            errors.append(f"无效的匹配方法: {template.method}")
        
        return errors
    
    @classmethod
    def _validate_name(cls, name: str) -> List[str]:
        """验证名称"""
        errors = []
        
        if not name:
            errors.append("名称不能为空")
            return errors
        
        if len(name) < cls.MIN_NAME_LENGTH:
            errors.append(f"名称过短（最少{cls.MIN_NAME_LENGTH}字符）")
        
        if len(name) > cls.MAX_NAME_LENGTH:
            errors.append(f"名称过长（最多{cls.MAX_NAME_LENGTH}字符）")
        
        if not cls.NAME_PATTERN.match(name):
            errors.append("名称包含无效字符（只允许字母、数字、中文、下划线、连字符和空格）")
        
        return errors
    
    @classmethod
    def _validate_category(cls, category: str) -> List[str]:
        """验证分类"""
        errors = []
        
        try:
            TemplateCategory(category)
        except ValueError:
            valid_categories = [cat.value for cat in TemplateCategory]
            errors.append(f"无效的分类: {category}，有效值: {valid_categories}")
        
        return errors
    
    @classmethod
    def _validate_status(cls, status: str) -> List[str]:
        """验证状态"""
        errors = []
        
        try:
            TemplateStatus(status)
        except ValueError:
            valid_statuses = [stat.value for stat in TemplateStatus]
            errors.append(f"无效的状态: {status}，有效值: {valid_statuses}")
        
        return errors
    
    @classmethod
    def _validate_threshold(cls, threshold: float) -> List[str]:
        """验证阈值"""
        errors = []
        
        if not isinstance(threshold, (int, float)):
            errors.append("阈值必须是数字")
            return errors
        
        if threshold < 0.0 or threshold > 1.0:
            errors.append("阈值必须在0.0到1.0之间")
        
        return errors
    
    @classmethod
    def raise_if_invalid(cls, template: Template) -> None:
        """如果模板无效则抛出异常"""
        errors = cls.validate_template(template)
        if errors:
            raise TemplateValidationError(
                "template",
                template.to_dict(),
                f"模板验证失败: {'; '.join(errors)}"
            )


def validate_template_name_format(name: str) -> bool:
    """
    验证模板名称格式是否正确

    Args:
        name: 模板名称

    Returns:
        是否有效
    """
    return TemplateValidator._validate_name(name) == []


def clean_template_name(name: str) -> str:
    """
    清理模板名称，移除分类前缀

    Args:
        name: 原始模板名称

    Returns:
        清理后的名称
    """
    if '] ' in name:
        return name.split('] ', 1)[1]
    return name


def build_display_name(name: str, category: TemplateCategory) -> str:
    """
    构建显示名称，添加分类前缀

    Args:
        name: 模板名称
        category: 模板分类

    Returns:
        显示名称
    """
    if not name.startswith('['):
        return f"[{category.value}] {name}"
    return name
