#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板匹配代码 - 由新模板系统生成
模板: [通用] 本地磁盘
生成时间: 2025-07-28 00:12:15
"""

import sys
import cv2
import numpy as np
from pathlib import Path
import os

# 智能路径检测和设置
def setup_paths():
    """智能设置项目路径"""
    current_file = Path(__file__).resolve()
    current_dir = current_file.parent
    
    # 可能的项目根目录位置
    possible_roots = [
        current_dir,
        current_dir.parent,
        current_dir.parent.parent,
        current_dir.parent.parent.parent
    ]
    
    for root in possible_roots:
        # 查找包含opencv_yolo的目录
        opencv_yolo_dir = root / 'opencv_yolo'
        if opencv_yolo_dir.exists():
            src_path = opencv_yolo_dir / 'src'
            if src_path.exists():
                sys.path.insert(0, str(src_path))
                return opencv_yolo_dir
        
        # 查找src目录
        src_path = root / 'src'
        if src_path.exists():
            sys.path.insert(0, str(src_path))
            return root
    
    return current_dir

# 设置项目路径
PROJECT_ROOT = setup_paths()

# 模板配置
TEMPLATE_NAME = '本地磁盘'
TEMPLATE_THRESHOLD = 0.52
TEMPLATE_METHOD = cv2.TM_CCOEFF_NORMED
TEMPLATE_ORIGINAL_PATH = r'templates\本地磁盘_20250709_190219.png'

def find_template_file():
    """智能查找模板文件"""
    # 可能的模板路径
    possible_paths = [
        TEMPLATE_ORIGINAL_PATH,  # 原始路径
        'templates/本地磁盘.png',  # 新格式
        f'templates/本地磁盘_*.png',  # 时间戳格式
        PROJECT_ROOT / 'templates' / f'{TEMPLATE_NAME}.png',
        PROJECT_ROOT / 'opencv_yolo' / 'templates' / f'{TEMPLATE_NAME}.png',
        Path(__file__).parent / 'templates' / f'{TEMPLATE_NAME}.png'
    ]
    
    for path_pattern in possible_paths:
        if isinstance(path_pattern, str):
            if '*' in path_pattern:
                # 处理通配符模式
                import glob
                matches = glob.glob(str(path_pattern))
                if matches:
                    return Path(matches[0])
            else:
                path = Path(path_pattern)
        else:
            path = path_pattern
        
        if path.exists():
            return path
        
        # 尝试相对于项目根目录
        abs_path = PROJECT_ROOT / path
        if abs_path.exists():
            return abs_path
    
    return None

def load_template_image():
    """加载模板图像 - 优先使用新系统，回退到传统方法"""
    # 尝试使用新模板系统
    try:
        from yolo_opencv_detector.core.template_api import get_template_api
        api = get_template_api()
        image = api.load_template_image('本地磁盘')
        if image is not None:
            print(f'✅ 新系统加载模板成功: {image.shape}')
            return image
    except Exception as e:
        print(f'⚠️ 新系统加载失败: {e}')
    
    # 回退到传统方法 - 智能查找文件
    template_path = find_template_file()
    
    if template_path and template_path.exists():
        try:
            # 使用支持中文路径的方法
            image_data = np.fromfile(str(template_path), dtype=np.uint8)
            image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            if image is not None:
                print(f'✅ 传统方法加载模板成功: {image.shape}')
                print(f'   文件路径: {template_path}')
                return image
        except Exception as e:
            print(f'⚠️ 传统方法加载失败: {e}')
    else:
        print(f'⚠️ 未找到模板文件，尝试的路径:')
        print(f'   原始路径: {TEMPLATE_ORIGINAL_PATH}')
        print(f'   项目根目录: {PROJECT_ROOT}')
    
    print('❌ 模板加载失败')
    return None

def match_template_in_image(screenshot):
    """在截图中匹配模板"""
    template = load_template_image()
    if template is None:
        return None
    
    # 执行模板匹配
    result = cv2.matchTemplate(screenshot, template, TEMPLATE_METHOD)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    # 根据匹配方法选择最佳位置
    if TEMPLATE_METHOD in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        best_match = min_loc
        confidence = 1 - min_val
    else:
        best_match = max_loc
        confidence = max_val
    
    # 检查置信度
    if confidence >= TEMPLATE_THRESHOLD:
        h, w = template.shape[:2]
        return {
            'position': best_match,
            'confidence': confidence,
            'bbox': (best_match[0], best_match[1], w, h),
            'center': (best_match[0] + w//2, best_match[1] + h//2)
        }
    
    return None

def main():
    """主函数 - 演示模板匹配"""
    print('🎯 模板匹配演示 - [通用] 本地磁盘')
    print(f'📊 模板名称: {TEMPLATE_NAME}')
    print(f'📊 匹配阈值: {TEMPLATE_THRESHOLD}')
    print(f'📊 匹配方法: {TEMPLATE_METHOD}')
    print()
    
    # 截取屏幕
    try:
        import pyautogui
        screenshot = pyautogui.screenshot()
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        print(f'📸 截图尺寸: {screenshot.shape}')
    except Exception as e:
        print(f'❌ 截图失败: {e}')
        return
    
    # 执行模板匹配
    match_result = match_template_in_image(screenshot)
    
    if match_result:
        print(f'✅ 找到匹配目标!')
        print(f'   位置: {match_result["position"]}')
        print(f'   置信度: {match_result["confidence"]:.3f}')
        print(f'   边界框: {match_result["bbox"]}')
        print(f'   中心点: {match_result["center"]}')
    else:
        print('❌ 未找到匹配目标')
    
    print('\n✅ 演示完成!')

if __name__ == '__main__':
    main()