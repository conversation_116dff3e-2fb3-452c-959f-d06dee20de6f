#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（测试版本）
导出时间: 2025-07-27T11:35:46.420829

此脚本是导出时刻GUI状态的完全静态快照，包含以下固化参数：
- 置信度阈值: 0.50
- NMS阈值: 0.40
- 选择模板: 无
- 检测间隔: 1.0秒
- 自动检测: False

注意：此脚本不会动态读取GUI配置，参数已固化。
如需不同参数，请重新生成新的状态快照。
"""

# ============================================================================
# 导出时刻的固化参数（静态快照）
# ============================================================================
EXPORTED_CONFIDENCE_THRESHOLD = 0.500
EXPORTED_NMS_THRESHOLD = 0.400
EXPORTED_TEMPLATE_NAME = None
EXPORTED_DETECTION_INTERVAL = 1.0
EXPORTED_AUTO_DETECT = False
EXPORTED_TIMESTAMP = "2025-07-27T11:35:46.420829"

print(f"🎯 YOLO检测器 - 静态快照版本（测试）")
print(f"📅 导出时间: {EXPORTED_TIMESTAMP}")
print(f"⚙️  固化参数:")
print(f"   • 置信度阈值: {EXPORTED_CONFIDENCE_THRESHOLD}")
print(f"   • NMS阈值: {EXPORTED_NMS_THRESHOLD}")
print(f"   • 选择模板: {EXPORTED_TEMPLATE_NAME}")
print(f"   • 检测间隔: {EXPORTED_DETECTION_INTERVAL}秒")
print(f"   • 自动检测: {EXPORTED_AUTO_DETECT}")

def main():
    print("✅ 静态快照代码测试成功！")

if __name__ == "__main__":
    main()
