#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的模板加载功能
作者: Cursor AI
创建时间: 2025-07-27
"""

import os
import re
import sys
from pathlib import Path

# 复制修复后的函数，避免导入GUI模块
def load_template_image_by_name(template_name):
    """根据模板名称加载图像 - 支持智能文件名匹配"""
    try:
        import cv2
        import numpy as np
        
        print(f"🔍 加载模板图像: {template_name}")

        # 清理模板名称，移除类别前缀
        clean_name = template_name
        if '] ' in template_name:
            clean_name = template_name.split('] ', 1)[1]

        print(f"   清理后的模板名称: {clean_name}")

        # 定义可能的模板目录
        template_dirs = [
            Path('templates'),
            Path('opencv_yolo/templates'),
            Path('3_in_1/opencv_yolo/templates'),
        ]

        # 支持的图像扩展名
        extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']

        # 1. 精确匹配
        print("🔍 尝试精确匹配...")
        for template_dir in template_dirs:
            if not template_dir.exists():
                continue
            for ext in extensions:
                exact_path = template_dir / f"{clean_name}{ext}"
                if exact_path.exists():
                    print(f"   ✅ 精确匹配找到: {exact_path}")
                    return _load_image_with_chinese_support(exact_path)

        # 2. 模糊匹配（处理带时间戳的文件名）
        print("🔍 尝试模糊匹配...")
        for template_dir in template_dirs:
            if not template_dir.exists():
                continue
            
            try:
                for file_path in template_dir.iterdir():
                    if file_path.is_file() and file_path.suffix.lower() in extensions:
                        # 检查文件名是否包含模板名称
                        if clean_name.lower() in file_path.stem.lower():
                            print(f"   ✅ 模糊匹配找到: {file_path}")
                            return _load_image_with_chinese_support(file_path)
                        
                        # 检查去除特殊字符后的匹配
                        clean_template = re.sub(r'[^\w\u4e00-\u9fff]', '', clean_name)
                        clean_filename = re.sub(r'[^\w\u4e00-\u9fff]', '', file_path.stem)
                        if clean_template and clean_template.lower() in clean_filename.lower():
                            print(f"   ✅ 清理后匹配找到: {file_path}")
                            return _load_image_with_chinese_support(file_path)
                            
            except Exception as e:
                print(f"   模糊匹配过程出错: {e}")
                continue

        print(f"❌ 未找到模板文件: {clean_name}")
        return None

    except Exception as e:
        print(f"❌ 加载模板图像失败: {e}")
        return None


def _load_image_with_chinese_support(file_path):
    """使用支持中文文件名的方法加载图像"""
    try:
        import cv2
        import numpy as np
        
        print(f"   尝试加载: {file_path}")
        # 使用支持中文文件名的方法
        image_data = np.fromfile(str(file_path), dtype=np.uint8)
        image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
        if image is not None:
            print(f"   ✅ 成功加载模板图像: {file_path}")
            print(f"   图像尺寸: {image.shape}")
            return image
        else:
            print(f"   ❌ 图像解码失败: {file_path}")
            return None
    except Exception as e:
        print(f"   ❌ 加载失败: {e}")
        return None


def test_static_code_template_loading():
    """测试静态代码中的模板加载功能"""
    print("🧪 测试静态代码中的模板加载功能")
    print("=" * 50)

    # 模拟静态代码中的模板配置
    EXPORTED_TEMPLATE_MATCHING_ENABLED = True
    EXPORTED_TEMPLATE_DATA = {
        'name': '[通用] 本地磁盘',
        'threshold': 0.8
    }

    print(f"📊 模板匹配启用: {EXPORTED_TEMPLATE_MATCHING_ENABLED}")
    print(f"📊 模板数据: {EXPORTED_TEMPLATE_DATA}")

    if EXPORTED_TEMPLATE_MATCHING_ENABLED and EXPORTED_TEMPLATE_DATA:
        template_name = EXPORTED_TEMPLATE_DATA['name']
        print(f"\n🔍 测试加载模板: {template_name}")
        
        # 使用修复后的函数加载模板
        image = load_template_image_by_name(template_name)
        
        if image is not None:
            print(f"✅ 模板加载成功！")
            print(f"   图像尺寸: {image.shape}")
            print("🎉 静态代码的模板匹配功能已修复！")
            return True
        else:
            print("❌ 模板加载失败")
            return False
    else:
        print("⚠️ 模板匹配未启用")
        return False


if __name__ == "__main__":
    print("🚀 开始测试修复后的模板加载功能...")
    
    # 设置工作目录
    current_dir = Path(__file__).parent
    os.chdir(current_dir)
    print(f"📍 当前工作目录: {os.getcwd()}")
    
    # 测试模板加载
    success = test_static_code_template_loading()
    
    if success:
        print("\n🎉 测试完成！静态代码的模板加载功能已成功修复")
        print("💡 现在静态代码可以正确找到并加载带时间戳的模板文件")
    else:
        print("\n⚠️ 测试完成，但仍需进一步调试")
