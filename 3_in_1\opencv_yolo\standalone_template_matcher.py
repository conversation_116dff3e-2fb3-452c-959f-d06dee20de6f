#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立模板匹配代码 - 本地磁盘
生成时间: 2025-07-27 24:00:00
文件: 本地磁盘_20250709_190219.png

这是一个完全独立的模板匹配代码，不依赖项目的其他模块。
"""

import cv2
import numpy as np
from pathlib import Path
import time

def find_template_file():
    """查找模板文件"""
    template_file = "本地磁盘_20250709_190219.png"
    current_dir = Path(__file__).parent
    
    print(f"🔍 查找模板文件: {template_file}")
    print(f"📁 当前目录: {current_dir}")
    
    # 可能的模板路径
    possible_paths = [
        current_dir / 'templates' / template_file,
        current_dir.parent / 'opencv_yolo' / 'templates' / template_file,
        current_dir.parent.parent / 'opencv_yolo' / 'templates' / template_file,
        current_dir / template_file,  # 直接在当前目录
    ]
    
    for i, path in enumerate(possible_paths, 1):
        print(f"  {i}. 检查: {path}")
        if path.exists():
            print(f"     ✅ 找到文件!")
            return path
        else:
            print(f"     ❌ 不存在")
    
    print(f"❌ 未找到模板文件")
    return None

def load_template_image():
    """加载模板图像"""
    template_path = find_template_file()
    
    if not template_path:
        return None
    
    try:
        print(f"\n📸 加载模板图像: {template_path}")
        # 使用支持中文路径的方法
        image_data = np.fromfile(str(template_path), dtype=np.uint8)
        template = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
        
        if template is not None:
            print(f"✅ 模板加载成功: {template.shape}")
            return template
        else:
            print(f"❌ 模板解码失败")
            return None
    except Exception as e:
        print(f"❌ 模板加载失败: {e}")
        return None

def capture_screenshot():
    """截取屏幕"""
    try:
        print(f"\n📸 截取屏幕...")
        import pyautogui
        screenshot = pyautogui.screenshot()
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        print(f"✅ 截图成功: {screenshot.shape}")
        return screenshot
    except Exception as e:
        print(f"❌ 截图失败: {e}")
        return None

def match_template(screenshot, template, threshold=0.8):
    """执行模板匹配"""
    if screenshot is None or template is None:
        print(f"❌ 输入参数无效")
        return None
    
    try:
        print(f"\n🔍 执行模板匹配...")
        print(f"   截图尺寸: {screenshot.shape}")
        print(f"   模板尺寸: {template.shape}")
        print(f"   匹配阈值: {threshold}")
        
        # 执行模板匹配
        result = cv2.matchTemplate(screenshot, template, cv2.TM_CCOEFF_NORMED)
        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
        
        print(f"\n📊 匹配结果:")
        print(f"   最小值: {min_val:.3f}")
        print(f"   最大值: {max_val:.3f}")
        print(f"   最小位置: {min_loc}")
        print(f"   最大位置: {max_loc}")
        
        if max_val >= threshold:
            h, w = template.shape[:2]
            center = (max_loc[0] + w//2, max_loc[1] + h//2)
            
            result_data = {
                'found': True,
                'confidence': max_val,
                'position': max_loc,
                'center': center,
                'bbox': (max_loc[0], max_loc[1], w, h),
                'template_size': (w, h)
            }
            
            print(f"✅ 找到匹配目标!")
            print(f"   位置: {max_loc}")
            print(f"   置信度: {max_val:.3f}")
            print(f"   中心点: {center}")
            print(f"   边界框: {result_data['bbox']}")
            
            return result_data
        else:
            print(f"❌ 未找到匹配目标 (置信度: {max_val:.3f} < {threshold})")
            return {'found': False, 'confidence': max_val, 'threshold': threshold}
            
    except Exception as e:
        print(f"❌ 模板匹配失败: {e}")
        return None

def click_if_found(match_result):
    """如果找到目标，则点击"""
    if not match_result or not match_result.get('found'):
        print(f"❌ 未找到目标，无法点击")
        return False
    
    try:
        import pyautogui
        center = match_result['center']
        print(f"\n🖱️ 点击目标中心: {center}")
        pyautogui.click(center[0], center[1])
        print(f"✅ 点击成功")
        return True
    except Exception as e:
        print(f"❌ 点击失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 独立模板匹配演示 - 本地磁盘")
    print("=" * 50)
    
    # 配置参数
    template_name = "本地磁盘"
    threshold = 0.8  # 可以调整这个阈值
    auto_click = False  # 是否自动点击找到的目标
    
    print(f"📋 配置:")
    print(f"   模板名称: {template_name}")
    print(f"   匹配阈值: {threshold}")
    print(f"   自动点击: {auto_click}")
    
    # 步骤1: 加载模板
    template = load_template_image()
    if template is None:
        print(f"\n❌ 模板加载失败，程序退出")
        return
    
    # 步骤2: 截取屏幕
    screenshot = capture_screenshot()
    if screenshot is None:
        print(f"\n❌ 截图失败，程序退出")
        return
    
    # 步骤3: 执行匹配
    match_result = match_template(screenshot, template, threshold)
    
    # 步骤4: 处理结果
    if match_result and match_result.get('found'):
        print(f"\n🎉 匹配成功!")
        
        if auto_click:
            click_if_found(match_result)
        else:
            print(f"💡 如需自动点击，请将 auto_click 设置为 True")
    else:
        print(f"\n😔 未找到匹配目标")
        if match_result:
            confidence = match_result.get('confidence', 0)
            threshold_used = match_result.get('threshold', threshold)
            print(f"💡 建议:")
            print(f"   - 当前置信度: {confidence:.3f}")
            print(f"   - 当前阈值: {threshold_used}")
            print(f"   - 可以尝试降低阈值到 {confidence + 0.1:.1f} 左右")
    
    print(f"\n✅ 程序执行完成!")

if __name__ == '__main__':
    main()
