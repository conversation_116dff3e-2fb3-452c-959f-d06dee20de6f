@echo off
chcp 65001 >nul
title 22 - YOLO Detection Script

echo ========================================
echo YOLO Detection Standalone Script
echo ========================================
echo.

REM Check for project virtual environment first
if exist "..\..\..\shared_env\Scripts\python.exe" (
    echo Found project virtual environment...
    echo Using: ..\..\..\shared_env\Scripts\python.exe
    set PYTHON_EXE="..\..\..\shared_env\Scripts\python.exe"
    goto :python_found
)

if exist "venv\Scripts\activate.bat" (
    echo Activating local virtual environment...
    call venv\Scripts\activate.bat
    set PYTHON_EXE=python
    goto :python_found
)

echo ERROR: No suitable Python environment found!
echo.
echo Please ensure one of the following:
echo 1. Project virtual environment exists at: ..\..\..\shared_env\Scripts\python.exe
echo 2. Local virtual environment exists at: venv\Scripts\activate.bat
echo.
echo To create the project virtual environment, run:
echo   cd ..\..\..\
echo   python -m venv shared_env
echo   shared_env\Scripts\pip.exe install -r requirements.txt
echo.
pause
exit /b 1

:python_found

REM Check if Python is available
%PYTHON_EXE% --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python is not available at %PYTHON_EXE%
    echo Please check your Python installation
    pause
    exit /b 1
)

echo Python version:
%PYTHON_EXE% --version

REM Check if requirements are installed
echo Checking dependencies...
%PYTHON_EXE% -c "import cv2, numpy, PIL" >nul 2>&1
if errorlevel 1 (
    echo Installing required packages...
    %PYTHON_EXE% -m pip install -r requirements.txt
    if errorlevel 1 (
        echo Error: Failed to install dependencies
        echo Please check your internet connection and try again
        pause
        exit /b 1
    )
)

echo.
echo Starting detection script...
echo Using Python: %PYTHON_EXE%
echo.

REM Run the main script
%PYTHON_EXE% main.py

REM Keep window open
echo.
echo Script execution completed.
pause
