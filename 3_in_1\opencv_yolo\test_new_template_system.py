#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新模板管理系统测试

测试新的模板管理架构的功能和性能。

Created: 2025-07-27
Author: Augment Agent
"""

import sys
import os
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_template_api():
    """测试模板API"""
    print("🧪 测试模板API...")
    
    try:
        from yolo_opencv_detector.core.template_api import get_template_api, clean_template_name_func
        
        # 获取API实例
        api = get_template_api("test_templates")
        
        print("✅ API实例创建成功")
        
        # 测试名称清理
        test_names = [
            "[通用] 本地磁盘",
            "[按钮] 登录按钮",
            "普通名称"
        ]
        
        for name in test_names:
            clean_name = clean_template_name_func(name)
            print(f"  名称清理: '{name}' -> '{clean_name}'")
        
        # 测试模板创建（如果有测试图像）
        test_image_path = project_root / "templates" / "本地磁盘_20250709_190219.png"
        if test_image_path.exists():
            print(f"🔍 找到测试图像: {test_image_path}")
            
            result = api.create_template_from_file(
                name="测试模板",
                image_path=test_image_path,
                category="通用",
                description="这是一个测试模板"
            )
            
            if result['success']:
                print(f"✅ 模板创建成功: {result['template_id']}")
                print(f"  清理后名称: {result['name']}")
                print(f"  显示名称: {result['display_name']}")
                
                # 测试获取模板
                template_info = api.get_template_by_name("测试模板")
                if template_info:
                    print("✅ 模板获取成功")
                    print(f"  ID: {template_info['id']}")
                    print(f"  分类: {template_info['category']}")
                    print(f"  路径: {template_info['path']}")
                
                # 测试加载图像
                image = api.load_template_image("测试模板")
                if image is not None:
                    print(f"✅ 图像加载成功: {image.shape}")
                else:
                    print("⚠️ 图像加载失败")
                
                # 测试删除模板
                if api.delete_template("测试模板"):
                    print("✅ 模板删除成功")
                else:
                    print("⚠️ 模板删除失败")
            else:
                print(f"❌ 模板创建失败: {result['error']}")
        else:
            print("⚠️ 未找到测试图像，跳过创建测试")
        
        # 测试统计信息
        stats = api.get_template_stats()
        print("📊 系统统计信息:")
        print(f"  存储目录: {stats['manager_info']['storage_dir']}")
        print(f"  模板数量: {stats['storage']['template_count']}")
        print(f"  缓存大小: {stats['cache']['metadata_cache_size']}")
        
        print("✅ 模板API测试完成\n")
        
    except Exception as e:
        print(f"❌ 模板API测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_template_models():
    """测试模板数据模型"""
    print("🧪 测试模板数据模型...")
    
    try:
        from yolo_opencv_detector.core.template_models import (
            Template, TemplateCategory, TemplateStatus, TemplateMetadata
        )
        
        # 创建模板对象
        template = Template(
            id="test-template-001",
            name="测试模板",
            display_name="[通用] 测试模板",
            description="这是一个测试模板",
            category=TemplateCategory.GENERAL,
            status=TemplateStatus.ACTIVE,
            threshold=0.8,
            method="TM_CCOEFF_NORMED"
        )
        
        print("✅ 模板对象创建成功")
        print(f"  ID: {template.id}")
        print(f"  清理后名称: {template.clean_name}")
        print(f"  完整显示名称: {template.full_display_name}")
        print(f"  是否有效: {template.is_valid}")
        
        # 测试序列化
        template_dict = template.to_dict()
        print("✅ 模板序列化成功")
        
        # 测试反序列化
        restored_template = Template.from_dict(template_dict)
        print("✅ 模板反序列化成功")
        print(f"  恢复的模板ID: {restored_template.id}")
        
        # 测试更新
        updated_template = template.update(description="更新后的描述")
        print("✅ 模板更新成功")
        print(f"  新描述: {updated_template.description}")
        print(f"  原模板描述: {template.description}")  # 验证不可变性
        
        print("✅ 模板数据模型测试完成\n")
        
    except Exception as e:
        print(f"❌ 模板数据模型测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_template_validator():
    """测试模板验证器"""
    print("🧪 测试模板验证器...")
    
    try:
        from yolo_opencv_detector.core.template_validator import (
            TemplateValidator, validate_template_name_format, 
            clean_template_name, build_display_name
        )
        from yolo_opencv_detector.core.template_models import TemplateCategory
        
        # 测试名称验证
        test_names = [
            "有效名称",
            "Valid_Name_123",
            "中文名称测试",
            "",  # 无效：空名称
            "a" * 200,  # 无效：过长
            "invalid@name",  # 无效：特殊字符
        ]
        
        for name in test_names:
            is_valid = validate_template_name_format(name)
            print(f"  名称验证: '{name[:20]}...' -> {'✅' if is_valid else '❌'}")
        
        # 测试名称清理
        test_display_names = [
            "[通用] 本地磁盘",
            "[按钮] 登录按钮",
            "普通名称"
        ]
        
        for display_name in test_display_names:
            clean_name = clean_template_name(display_name)
            rebuilt_name = build_display_name(clean_name, TemplateCategory.GENERAL)
            print(f"  名称处理: '{display_name}' -> '{clean_name}' -> '{rebuilt_name}'")
        
        print("✅ 模板验证器测试完成\n")
        
    except Exception as e:
        print(f"❌ 模板验证器测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_template_cache():
    """测试模板缓存"""
    print("🧪 测试模板缓存...")
    
    try:
        from yolo_opencv_detector.core.template_cache import TemplateCache
        import numpy as np
        
        # 创建缓存实例
        cache = TemplateCache(image_cache_size=5, max_memory_mb=10)
        
        print("✅ 缓存实例创建成功")
        
        # 创建测试图像
        test_image = np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8)
        
        # 测试图像缓存
        cache.image_cache.put_image("test-001", test_image, Path("test.png"))
        print("✅ 图像缓存成功")
        
        # 测试图像获取
        cached_image = cache.image_cache.get_image("test-001")
        if cached_image is not None:
            print(f"✅ 图像获取成功: {cached_image.shape}")
        else:
            print("❌ 图像获取失败")
        
        # 测试缓存统计
        stats = cache.get_stats()
        print("📊 缓存统计:")
        print(f"  图像缓存大小: {stats['image_cache']['cache_size']}")
        print(f"  内存使用: {stats['image_cache']['memory_usage_mb']:.2f} MB")
        
        # 清空缓存
        cache.clear_all()
        print("✅ 缓存清空成功")
        
        print("✅ 模板缓存测试完成\n")
        
    except Exception as e:
        print(f"❌ 模板缓存测试失败: {e}")
        import traceback
        traceback.print_exc()


def test_performance():
    """测试性能"""
    print("🧪 测试性能...")
    
    try:
        from yolo_opencv_detector.core.template_api import get_template_api
        
        api = get_template_api("perf_test_templates")
        
        # 测试名称清理性能
        test_names = ["[通用] 测试模板"] * 1000
        
        start_time = time.time()
        for name in test_names:
            api.clean_name(name)
        end_time = time.time()
        
        print(f"✅ 名称清理性能: {len(test_names)} 次操作耗时 {(end_time - start_time)*1000:.2f} ms")
        
        # 测试缓存性能
        stats = api.get_template_stats()
        print(f"📊 系统性能统计:")
        print(f"  缓存命中率: 模拟测试")
        print(f"  内存使用: {stats['cache']['image_cache']['memory_usage_mb']:.2f} MB")
        
        print("✅ 性能测试完成\n")
        
    except Exception as e:
        print(f"❌ 性能测试失败: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 开始测试新模板管理系统...")
    print("=" * 60)
    
    test_template_models()
    test_template_validator()
    test_template_cache()
    test_template_api()
    test_performance()
    
    print("=" * 60)
    print("✅ 所有测试完成")


if __name__ == "__main__":
    main()
