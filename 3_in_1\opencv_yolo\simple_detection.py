#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器 - 简单检测示例
这是一个基础的检测示例，展示如何使用YOLO检测器进行目标检测

作者: YOLO OpenCV检测器团队
创建时间: 2025-07-27
"""

import sys
import os
from pathlib import Path
import cv2
import numpy as np
from datetime import datetime

# 添加项目路径
current_dir = Path(__file__).parent.absolute()
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数 - 简单检测示例"""
    print("🎯 YOLO OpenCV检测器 - 简单检测示例")
    print("=" * 50)
    
    try:
        # 导入检测器组件
        from yolo_opencv_detector.core.yolo_detector_v2 import YOLODetectorV2
        from yolo_opencv_detector.core.screen_capture_service_v2 import ScreenCaptureServiceV2
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        print("✅ 模块导入成功")
        
        # 初始化组件
        print("🔧 初始化检测组件...")
        config_manager = ConfigManager()
        yolo_detector = YOLODetectorV2(config_manager)
        screen_capture = ScreenCaptureServiceV2()
        
        print("✅ 组件初始化完成")
        
        # 执行屏幕检测
        print("\n📸 执行屏幕截图...")
        image = screen_capture.capture_fullscreen()
        
        if image is None:
            print("❌ 屏幕截图失败")
            return False
        
        print(f"✅ 屏幕截图成功，尺寸: {image.shape}")
        
        # 执行YOLO检测
        print("\n🔍 执行YOLO目标检测...")
        detections = yolo_detector.detect(
            image,
            confidence=0.5,  # 置信度阈值
            nms_threshold=0.4  # NMS阈值
        )
        
        print(f"✅ 检测完成，发现 {len(detections)} 个目标")
        
        # 显示检测结果
        if detections:
            print("\n📋 检测结果详情:")
            for i, detection in enumerate(detections, 1):
                class_name = detection.class_name
                confidence = detection.confidence
                bbox = detection.bbox
                
                print(f"  目标 {i}:")
                print(f"    类别: {class_name}")
                print(f"    置信度: {confidence:.3f}")
                print(f"    位置: x={bbox[0]:.0f}, y={bbox[1]:.0f}, w={bbox[2]:.0f}, h={bbox[3]:.0f}")
                
                # 计算中心点
                center_x = int(bbox[0] + bbox[2] / 2)
                center_y = int(bbox[1] + bbox[3] / 2)
                print(f"    中心点: ({center_x}, {center_y})")
                print()
        else:
            print("ℹ️ 未检测到任何目标")
            print("💡 建议:")
            print("   • 确保屏幕上有可识别的物体")
            print("   • 尝试降低置信度阈值")
            print("   • 检查YOLO模型是否正确加载")
        
        # 保存检测结果图像（可选）
        if detections:
            print("💾 保存检测结果图像...")
            
            # 在图像上绘制检测框
            result_image = image.copy()
            for detection in detections:
                bbox = detection.bbox
                class_name = detection.class_name
                confidence = detection.confidence
                
                # 绘制边界框
                x, y, w, h = map(int, bbox)
                cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)
                
                # 绘制标签
                label = f"{class_name}: {confidence:.2f}"
                cv2.putText(result_image, label, (x, y - 10), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 2)
            
            # 保存图像
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = current_dir / "screenshots" / f"simple_detection_{timestamp}.png"
            output_path.parent.mkdir(exist_ok=True)
            
            cv2.imwrite(str(output_path), result_image)
            print(f"✅ 检测结果已保存到: {output_path}")
        
        print("\n🎉 简单检测示例执行完成！")
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {e}")
        print("💡 请确保:")
        print("   • 已正确安装所有依赖包")
        print("   • 项目路径设置正确")
        print("   • YOLO模型文件存在")
        return False
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        print("💡 请检查:")
        print("   • 系统权限设置")
        print("   • 屏幕截图功能")
        print("   • YOLO检测器配置")
        return False

def show_usage():
    """显示使用说明"""
    print("📚 使用说明:")
    print("=" * 30)
    print("1. 确保已安装所有依赖包")
    print("2. 确保YOLO模型文件存在于 models/ 目录")
    print("3. 运行此脚本进行简单的屏幕检测")
    print("4. 检测结果将显示在控制台")
    print("5. 如有检测到目标，结果图像将保存到 screenshots/ 目录")
    print()
    print("💡 提示:")
    print("• 可以修改 confidence 和 nms_threshold 参数调整检测精度")
    print("• 检测结果包括目标类别、置信度和位置信息")
    print("• 适合用于学习和测试YOLO检测功能")

if __name__ == "__main__":
    print("🚀 启动简单检测示例...")
    
    # 显示使用说明
    show_usage()
    
    # 询问用户是否继续
    try:
        user_input = input("\n按 Enter 键开始检测，或输入 'q' 退出: ").strip().lower()
        if user_input == 'q':
            print("👋 用户取消操作")
            sys.exit(0)
    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
        sys.exit(0)
    
    # 执行检测
    success = main()
    
    if success:
        print("\n✅ 示例执行成功！")
    else:
        print("\n❌ 示例执行失败！")
        print("📖 请查看上方的错误信息和建议")
    
    # 等待用户确认
    try:
        input("\n按 Enter 键退出...")
    except KeyboardInterrupt:
        pass
