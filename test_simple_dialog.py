#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试源代码对话框
"""

import sys
from pathlib import Path

def test_simple_dialog():
    """测试简单对话框创建"""
    try:
        print("🧪 测试简单对话框创建...")
        
        # 添加项目路径
        project_root = Path(__file__).parent / "3_in_1" / "opencv_yolo" / "src"
        sys.path.insert(0, str(project_root))
        
        # 创建QApplication
        from PyQt6.QtWidgets import QApplication
        app = QApplication(sys.argv)
        
        # 导入对话框类
        from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog
        
        # 检查类的方法
        print("🔍 检查类方法...")
        methods = [method for method in dir(SourceCodeDialog) if not method.startswith('_')]
        print(f"✅ 找到 {len(methods)} 个公共方法")
        
        if 'setup_dialog_icon' in dir(SourceCodeDialog):
            print("✅ setup_dialog_icon 方法存在")
        else:
            print("❌ setup_dialog_icon 方法不存在")
            print("📋 可用方法:", [m for m in dir(SourceCodeDialog) if 'setup' in m.lower()])
        
        # 尝试手动创建实例，跳过图标设置
        print("🔧 尝试手动创建实例...")
        
        # 创建一个修改版的对话框类
        class TestSourceCodeDialog(SourceCodeDialog):
            def __init__(self, parent=None):
                # 跳过父类的 __init__，直接调用 QDialog 的 __init__
                from PyQt6.QtWidgets import QDialog
                QDialog.__init__(self, parent)
                
                self.parent_window = parent
                self.execution_thread = None
                self.current_gui_state = None
                self.static_snapshot_code = None
                
                print("✅ 基础初始化完成")
        
        dialog = TestSourceCodeDialog()
        print("✅ 测试对话框创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_dialog()
    if success:
        print("\n✅ 简单对话框测试通过！")
    else:
        print("\n❌ 简单对话框测试失败！")
