#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板匹配代码 - 由新模板系统生成
模板: [通用] 本地磁盘
生成时间: 2025-07-27 23:40:56
"""

import sys
import cv2
import numpy as np
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
while not (project_root / 'src').exists() and project_root.parent != project_root:
    project_root = project_root.parent
src_path = project_root / 'src'
if src_path.exists():
    sys.path.insert(0, str(src_path))

# 模板配置
TEMPLATE_NAME = '本地磁盘'
TEMPLATE_THRESHOLD = 0.8
TEMPLATE_METHOD = cv2.TM_CCOEFF_NORMED
TEMPLATE_PATH = r'templates\本地磁盘_20250709_190219.png'

def load_template_image():
    """加载模板图像 - 优先使用新系统，回退到传统方法"""
    # 尝试使用新模板系统
    try:
        from yolo_opencv_detector.core.template_api import get_template_api
        api = get_template_api()
        image = api.load_template_image('本地磁盘')
        if image is not None:
            print(f'✅ 新系统加载模板成功: {image.shape}')
            return image
    except Exception as e:
        print(f'⚠️ 新系统加载失败: {e}')
    
    # 回退到传统方法
    template_path = Path(TEMPLATE_PATH)
    if not template_path.is_absolute():
        # 尝试相对于当前文件的路径
        template_path = Path(__file__).parent / TEMPLATE_PATH
    
    if template_path.exists():
        try:
            # 使用支持中文路径的方法
            image_data = np.fromfile(str(template_path), dtype=np.uint8)
            image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            if image is not None:
                print(f'✅ 传统方法加载模板成功: {image.shape}')
                return image
        except Exception as e:
            print(f'⚠️ 传统方法加载失败: {e}')
    
    print('❌ 模板加载失败')
    return None

def match_template_in_image(screenshot):
    """在截图中匹配模板"""
    template = load_template_image()
    if template is None:
        return None
    
    # 执行模板匹配
    result = cv2.matchTemplate(screenshot, template, TEMPLATE_METHOD)
    min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
    
    # 根据匹配方法选择最佳位置
    if TEMPLATE_METHOD in [cv2.TM_SQDIFF, cv2.TM_SQDIFF_NORMED]:
        best_match = min_loc
        confidence = 1 - min_val
    else:
        best_match = max_loc
        confidence = max_val
    
    # 检查置信度
    if confidence >= TEMPLATE_THRESHOLD:
        h, w = template.shape[:2]
        return {
            'position': best_match,
            'confidence': confidence,
            'bbox': (best_match[0], best_match[1], w, h),
            'center': (best_match[0] + w//2, best_match[1] + h//2)
        }
    
    return None

def main():
    """主函数 - 演示模板匹配"""
    print('🎯 模板匹配演示 - [通用] 本地磁盘')
    print(f'📊 模板名称: {TEMPLATE_NAME}')
    print(f'📊 匹配阈值: {TEMPLATE_THRESHOLD}')
    print(f'📊 匹配方法: {TEMPLATE_METHOD}')
    print()
    
    # 截取屏幕
    try:
        import pyautogui
        screenshot = pyautogui.screenshot()
        screenshot = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
        print(f'📸 截图尺寸: {screenshot.shape}')
    except Exception as e:
        print(f'❌ 截图失败: {e}')
        return
    
    # 执行模板匹配
    match_result = match_template_in_image(screenshot)
    
    if match_result:
        print(f'✅ 找到匹配目标!')
        print(f'   位置: {match_result["position"]}')
        print(f'   置信度: {match_result["confidence"]:.3f}')
        print(f'   边界框: {match_result["bbox"]}')
        print(f'   中心点: {match_result["center"]}')
    else:
        print('❌ 未找到匹配目标')
    
    print('\n✅ 演示完成!')

if __name__ == '__main__':
    main()