#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板管理API

提供简化的API接口，方便现有代码迁移到新的模板管理系统。

Created: 2025-07-27
Author: Augment Agent
"""

from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import numpy as np

from .template_manager import get_template_manager
from .template_models import Template, TemplateCategory, TemplateSearchCriteria
from .template_validator import clean_template_name, build_display_name


class TemplateAPI:
    """简化的模板管理API"""
    
    def __init__(self, storage_dir: Union[str, Path] = "templates"):
        self.manager = get_template_manager(storage_dir)
    
    def create_template_from_file(
        self,
        name: str,
        image_path: Union[str, Path],
        category: str = "通用",
        description: str = "",
        threshold: float = 0.8
    ) -> Dict[str, Any]:
        """
        从文件创建模板（兼容旧API）
        
        Args:
            name: 模板名称
            image_path: 图像文件路径
            category: 分类
            description: 描述
            threshold: 阈值
            
        Returns:
            操作结果字典
        """
        try:
            # 映射分类名称
            category_map = {
                "通用": TemplateCategory.GENERAL,
                "按钮": TemplateCategory.BUTTON,
                "图标": TemplateCategory.ICON,
                "文本": TemplateCategory.TEXT,
                "输入框": TemplateCategory.INPUT,
                "菜单": TemplateCategory.MENU,
                "对话框": TemplateCategory.DIALOG,
                "其他": TemplateCategory.OTHER
            }
            
            template_category = category_map.get(category, TemplateCategory.GENERAL)
            
            result = self.manager.create_template(
                name=name,
                image_path=image_path,
                category=template_category,
                description=description,
                threshold=threshold
            )
            
            if result.success:
                return {
                    'success': True,
                    'template_id': result.template.id,
                    'name': result.template.clean_name,
                    'display_name': result.template.full_display_name,
                    'path': str(result.template.file_path)
                }
            else:
                return {
                    'success': False,
                    'error': result.error_message,
                    'error_code': result.error_code
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'error_code': 'API_ERROR'
            }
    
    def get_template_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        根据名称获取模板（兼容旧API）
        
        Args:
            name: 模板名称（支持显示名称）
            
        Returns:
            模板信息字典或None
        """
        template = self.manager.get_template_by_name(name)
        if template:
            return self._template_to_dict(template)
        return None
    
    def get_template_with_image(self, name: str) -> Optional[Dict[str, Any]]:
        """
        获取模板及其图像（兼容旧API）
        
        Args:
            name: 模板名称
            
        Returns:
            包含图像的模板信息字典或None
        """
        template = self.manager.get_template_by_name(name)
        if not template:
            return None
        
        result = self.manager.get_template_with_image(template.id)
        if result:
            template_obj, image = result
            template_dict = self._template_to_dict(template_obj)
            template_dict['image'] = image
            return template_dict
        
        return None
    
    def load_template_image(self, name: str) -> Optional[np.ndarray]:
        """
        加载模板图像（兼容旧API）
        
        Args:
            name: 模板名称
            
        Returns:
            图像数组或None
        """
        template = self.manager.get_template_by_name(name)
        if not template:
            return None
        
        result = self.manager.get_template_with_image(template.id)
        if result:
            _, image = result
            return image
        
        return None
    
    def get_all_templates(self) -> List[Dict[str, Any]]:
        """
        获取所有模板（兼容旧API）
        
        Returns:
            模板信息字典列表
        """
        templates = self.manager.get_all_templates()
        return [self._template_to_dict(template) for template in templates.values()]
    
    def search_templates_by_category(self, category: str) -> List[Dict[str, Any]]:
        """
        按分类搜索模板
        
        Args:
            category: 分类名称
            
        Returns:
            模板信息字典列表
        """
        # 映射分类名称
        category_map = {
            "通用": TemplateCategory.GENERAL,
            "按钮": TemplateCategory.BUTTON,
            "图标": TemplateCategory.ICON,
            "文本": TemplateCategory.TEXT,
            "输入框": TemplateCategory.INPUT,
            "菜单": TemplateCategory.MENU,
            "对话框": TemplateCategory.DIALOG,
            "其他": TemplateCategory.OTHER
        }
        
        template_category = category_map.get(category)
        if not template_category:
            return []
        
        criteria = TemplateSearchCriteria(category=template_category)
        templates = self.manager.search_templates(criteria)
        
        return [self._template_to_dict(template) for template in templates]
    
    def delete_template(self, name: str) -> bool:
        """
        删除模板（兼容旧API）
        
        Args:
            name: 模板名称
            
        Returns:
            是否删除成功
        """
        template = self.manager.get_template_by_name(name)
        if not template:
            return False
        
        result = self.manager.delete_template(template.id)
        return result.success
    
    def update_template(self, name: str, **updates) -> bool:
        """
        更新模板（兼容旧API）
        
        Args:
            name: 模板名称
            **updates: 更新字段
            
        Returns:
            是否更新成功
        """
        template = self.manager.get_template_by_name(name)
        if not template:
            return False
        
        result = self.manager.update_template(template.id, **updates)
        return result.success
    
    def get_template_stats(self) -> Dict[str, Any]:
        """获取模板统计信息"""
        return self.manager.get_stats()
    
    def clean_name(self, name: str) -> str:
        """清理模板名称（移除分类前缀）"""
        return clean_template_name(name)
    
    def build_display_name(self, name: str, category: str) -> str:
        """构建显示名称（添加分类前缀）"""
        category_map = {
            "通用": TemplateCategory.GENERAL,
            "按钮": TemplateCategory.BUTTON,
            "图标": TemplateCategory.ICON,
            "文本": TemplateCategory.TEXT,
            "输入框": TemplateCategory.INPUT,
            "菜单": TemplateCategory.MENU,
            "对话框": TemplateCategory.DIALOG,
            "其他": TemplateCategory.OTHER
        }
        
        template_category = category_map.get(category, TemplateCategory.GENERAL)
        return build_display_name(name, template_category)
    
    def _template_to_dict(self, template: Template) -> Dict[str, Any]:
        """将模板对象转换为字典（兼容旧格式）"""
        return {
            'id': template.id,
            'name': template.clean_name,  # 清理后的名称
            'display_name': template.full_display_name,  # 完整显示名称
            'description': template.description,
            'category': template.category.value,
            'path': str(template.file_path),
            'relative_path': template.relative_path,
            'threshold': template.threshold,
            'method': template.method,
            'status': template.status.value,
            'created_time': template.metadata.created_time,
            'updated_time': template.metadata.updated_time,
            'usage_count': template.metadata.usage_count,
            'success_rate': template.metadata.success_rate,
            'tags': template.metadata.tags,
            'enabled': template.status.value == 'active'
        }


# 全局API实例
_global_template_api: Optional[TemplateAPI] = None


def get_template_api(storage_dir: Union[str, Path] = "templates") -> TemplateAPI:
    """
    获取全局模板API实例
    
    Args:
        storage_dir: 存储目录
        
    Returns:
        模板API实例
    """
    global _global_template_api
    if _global_template_api is None:
        _global_template_api = TemplateAPI(storage_dir)
    return _global_template_api


# 便捷函数（兼容旧代码）
def create_template(name: str, image_path: str, category: str = "通用", **kwargs) -> Dict[str, Any]:
    """创建模板的便捷函数"""
    api = get_template_api()
    return api.create_template_from_file(name, image_path, category, **kwargs)


def get_template(name: str) -> Optional[Dict[str, Any]]:
    """获取模板的便捷函数"""
    api = get_template_api()
    return api.get_template_by_name(name)


def load_template_image(name: str) -> Optional[np.ndarray]:
    """加载模板图像的便捷函数"""
    api = get_template_api()
    return api.load_template_image(name)


def get_all_templates() -> List[Dict[str, Any]]:
    """获取所有模板的便捷函数"""
    api = get_template_api()
    return api.get_all_templates()


def clean_template_name_func(name: str) -> str:
    """清理模板名称的便捷函数"""
    return clean_template_name(name)
