2025-07-05 12:43:15 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 加载检测配置失败: 'ConfigManager' object has no attribute 'get_detection_config'
2025-07-05 12:43:15 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 加载模板失败: 'ConfigManager' object has no attribute 'get_templates'
2025-07-05 12:43:15 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 加载配置失败: 'ConfigManager' object has no attribute 'get_yolo_config'
2025-07-05 15:21:30 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 15:34:56 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动模板截图失败
2025-07-05 15:35:14 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 15:43:29 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开预览对话框失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:43:31 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 15:44:29 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开预览对话框失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:44:30 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 15:45:00 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开预览对话框失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:45:00 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 15:49:06 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开预览对话框失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:49:09 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 15:49:10 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开截图工具失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:49:14 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开预览对话框失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:49:15 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动模板截图失败
2025-07-05 15:52:20 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 打开预览对话框失败: 'ImageViewerToolbar' object has no attribute 'update_zoom_info'
2025-07-05 15:52:23 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动模板截图失败
2025-07-05 15:52:39 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 设置图像失败: arguments did not match any overloaded call:
  setSceneRect(self, rect: QRectF): argument 1 has unexpected type 'QRect'
  setSceneRect(self, x: float, y: float, w: float, h: float): argument 1 has unexpected type 'QRect'
2025-07-05 15:53:37 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 设置图像失败: arguments did not match any overloaded call:
  setSceneRect(self, rect: QRectF): argument 1 has unexpected type 'QRect'
  setSceneRect(self, x: float, y: float, w: float, h: float): argument 1 has unexpected type 'QRect'
2025-07-05 15:53:43 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 16:07:40 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 16:08:35 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 16:09:27 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动检测截图失败
2025-07-05 16:41:30 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 添加模板失败: 'ConfigManager' object has no attribute 'save_templates'
2025-07-05 17:34:04 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 绘制检测结果失败: arguments did not match any overloaded call:
  QColor(color: Qt.GlobalColor): argument 1 has unexpected type 'tuple'
  QColor(rgb: int): too many arguments
  QColor(rgba64: QRgba64): argument 1 has unexpected type 'tuple'
  QColor(variant: Any): argument 1 has unexpected type 'tuple'
  QColor(): argument 1 has unexpected type 'tuple'
2025-07-05 17:56:10 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 添加模板失败: 'ConfigManager' object has no attribute 'save_templates'
2025-07-05 18:10:23 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动模板截图失败
2025-07-05 18:10:37 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动模板截图失败
2025-07-05 22:08:47 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动自动化窗口失败: No module named 'yolo_opencv_detector.core.detection_service'
2025-07-05 22:08:51 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动自动化窗口失败: No module named 'yolo_opencv_detector.core.detection_service'
2025-07-05 22:10:30 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 启动自动化窗口失败: Logger.get_logger() missing 1 required positional argument: 'name'
2025-07-06 06:36:03 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'tuple' object has no attribute 'get'
2025-07-06 06:36:03 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'tuple' object has no attribute 'get'
2025-07-06 08:23:51 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'QHBoxLayout' object has no attribute 'addSeparator'
2025-07-06 08:26:41 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'QHBoxLayout' object has no attribute 'addSeparator'
2025-07-06 10:30:57 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:30:58 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:30:58 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:30:59 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:30:59 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:00 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:00 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:01 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:01 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:02 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:02 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:03 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:31:03 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 10:41:31 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: unterminated triple-quoted string literal (detected at line 1409) (source_code_dialog.py, line 1244)
2025-07-06 10:41:39 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: unterminated triple-quoted string literal (detected at line 1409) (source_code_dialog.py, line 1244)
2025-07-06 10:48:59 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: No module named 'yolo_opencv_detector.gui.dialogs.source_code_dialog'
2025-07-06 13:26:45 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 13:26:46 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 13:26:47 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 13:26:48 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:508 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-06 15:06:17 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'setup_dialog_icon'
2025-07-06 15:46:10 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: invalid character '：' (U+FF1A) (source_code_dialog.py, line 1620)
2025-07-06 15:46:48 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: invalid character '：' (U+FF1A) (source_code_dialog.py, line 1620)
2025-07-09 18:14:24 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:25 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:26 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:28 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:29 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:29 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:14:30 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:678 | 绘制单个结果失败: 'list' object has no attribute 'get'
2025-07-09 18:43:52 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 0: 无法转换类型 <class 'str'>，跳过
2025-07-09 18:43:52 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 1: 无法转换类型 <class 'int'>，跳过
2025-07-09 18:43:52 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 2: 无法转换类型 <class 'NoneType'>，跳过
2025-07-09 18:43:52 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 3: 无法转换类型 <class 'list'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 0: 无法转换类型 <class 'NoneType'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 1: 无法转换类型 <class 'NoneType'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 0: 无法转换类型 <class 'str'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 1: 无法转换类型 <class 'int'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 2: 无法转换类型 <class 'list'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:set_detection_results:470 | 结果 4: 无法转换类型 <class 'NoneType'>，跳过
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:687 | 未知的边界框格式: <class 'str'>, 值: invalid
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:687 | 未知的边界框格式: <class 'list'>, 值: []
2025-07-09 18:48:27 | ERROR    | yolo_opencv_detector.gui.widgets.screenshot_widget:_draw_single_result:687 | 未知的边界框格式: <class 'list'>, 值: [1, 2]
2025-07-27 09:59:05 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 09:59:05 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 10:05:14 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 10:05:14 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 12:09:38 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: unterminated triple-quoted string literal (detected at line 3999) (source_code_dialog.py, line 3984)
2025-07-27 12:09:51 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: unterminated triple-quoted string literal (detected at line 3999) (source_code_dialog.py, line 3984)
2025-07-27 12:10:11 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: unterminated triple-quoted string literal (detected at line 3999) (source_code_dialog.py, line 3984)
2025-07-27 12:22:07 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'setup_dialog_icon'
2025-07-27 12:25:34 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'setup_dialog_icon'
2025-07-27 12:35:39 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'create_gui_detector_tab'
2025-07-27 12:36:23 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'create_gui_detector_tab'
2025-07-27 12:37:58 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'create_gui_detector_tab'
2025-07-27 12:38:10 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'create_gui_detector_tab'
2025-07-27 12:39:06 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: 'SourceCodeDialog' object has no attribute 'create_gui_detector_tab'
2025-07-27 13:59:09 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 13:59:09 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 13:59:52 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 13:59:52 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 15:22:29 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 15:22:29 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 15:30:57 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 15:30:57 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 15:37:27 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 15:37:27 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 16:20:05 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 16:20:05 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 16:29:57 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型文件不存在: models/yolov8n.pt
2025-07-27 16:29:57 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | YOLO模型未加载
2025-07-27 17:21:33 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: f-string expression part cannot include a backslash (source_code_dialog.py, line 1967)
2025-07-27 17:21:33 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 简化版源代码对话框也失败: f-string expression part cannot include a backslash (source_code_dialog.py, line 1967)
2025-07-27 20:33:52 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: name 'code_editor' is not defined
2025-07-27 20:34:05 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 显示源代码失败: name 'code_editor' is not defined
2025-07-27 23:02:49 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 截取模板失败: cannot import name 'TemplateCaptureDialog' from 'yolo_opencv_detector.gui.template_capture_dialog_v2' (C:\Users\<USER>\Documents\【看见上海】2025.05.12\Autowork_3in1_2025.7.13(模板预览修复支持中文)\3_in_1\opencv_yolo\src\yolo_opencv_detector\gui\template_capture_dialog_v2.py)
2025-07-27 23:03:06 | ERROR    | yolo_opencv_detector.utils.logger:error:170 | 截取模板失败: cannot import name 'TemplateCaptureDialog' from 'yolo_opencv_detector.gui.template_capture_dialog_v2' (C:\Users\<USER>\Documents\【看见上海】2025.05.12\Autowork_3in1_2025.7.13(模板预览修复支持中文)\3_in_1\opencv_yolo\src\yolo_opencv_detector\gui\template_capture_dialog_v2.py)
