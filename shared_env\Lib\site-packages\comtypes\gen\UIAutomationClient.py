from enum import IntFlag

import comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 as __wrapper_module__
from comtypes.gen._944DE083_8FB8_45CF_BCB7_C477ACB2F897_0_1_0 import (
    UIA_ValueIsReadOnlyPropertyId, UIA_CapStyleAttributeId,
    UIA_ItemContainerPatternId, UIA_CalendarControlTypeId, tagRECT,
    UIA_StylesFillPatternStylePropertyId,
    UIA_CaretPositionAttributeId, UIA_TableRowOrColumnMajorPropertyId,
    UIA_WindowControlTypeId, TreeScope_Subtree,
    UIA_DocumentControlTypeId, NotificationKind_ItemAdded,
    UIA_MenuClosedEventId, UIA_LegacyIAccessibleHelpPropertyId,
    UIA_ListControlTypeId, UIA_LegacyIAccessibleRolePropertyId,
    AnnotationType_Insertion<PERSON><PERSON>e,
    StructureChangeType_ChildrenBulkAdded,
    UIA_TableItemRowHeaderItemsPropertyId, HeadingLevel9,
    IRawElementProviderSimple, UIA_SemanticZoomControlTypeId,
    TextUnit_Word, UIA_SelectionItemPatternId,
    UIA_SayAsInterpretAsMetadataId, WSTRING,
    SupportedTextSelection_None,
    UIA_IsSpreadsheetPatternAvailablePropertyId,
    IUIAutomationElement7, UIA_AnnotationPatternId,
    UIA_Text_TextSelectionChangedEventId,
    UIA_TextEdit_ConversionTargetChangedEventId, DockPosition_Left,
    UIA_IsGridItemPatternAvailablePropertyId, UIA_ButtonControlTypeId,
    StyleId_BulletedList, AnnotationType_EditingLockedChange,
    UIA_SpinnerControlTypeId,
    UIA_Selection2CurrentSelectedItemPropertyId, UIA_TextPatternId,
    UIA_LocalizedLandmarkTypePropertyId,
    IUIAutomationDropTargetPattern,
    UIA_IsRangeValuePatternAvailablePropertyId,
    UIA_OutlineColorPropertyId, UIA_NavigationLandmarkTypeId,
    OrientationType_Horizontal, NotificationKind_ItemRemoved,
    UIA_StructureChangedEventId, IUIAutomationDragPattern,
    StructureChangeType_ChildRemoved, UIA_ProcessIdPropertyId,
    UIA_CustomControlTypeId, TextUnit_Character,
    IUIAutomationProxyFactoryMapping,
    AnnotationType_AdvancedProofingIssue,
    IUIAutomationObjectModelPattern, UIA_TextEdit_TextChangedEventId,
    UIA_ImageControlTypeId, StyleId_Heading5,
    IUIAutomationEventHandlerGroup,
    UIA_IsScrollItemPatternAvailablePropertyId,
    UIA_SpreadsheetItemFormulaPropertyId, HeadingLevel3,
    UIA_HeadingLevelPropertyId, UIA_TransformCanRotatePropertyId,
    UIA_SelectionItem_ElementAddedToSelectionEventId,
    TreeScope_Parent, UIA_IsObjectModelPatternAvailablePropertyId,
    IUIAutomationExpandCollapsePattern, TreeScope_Children,
    UIA_IsSelectionPattern2AvailablePropertyId,
    UIA_IsPasswordPropertyId, UIA_OverlineColorAttributeId,
    UIA_AppBarControlTypeId, UIA_Selection2ItemCountPropertyId,
    NavigateDirection_NextSibling, UIA_ValuePatternId,
    IUIAutomationMultipleViewPattern, UIA_ProgressBarControlTypeId,
    UIA_DropTargetDropTargetEffectsPropertyId,
    UIA_TextFlowDirectionsAttributeId,
    UIA_TransformCanResizePropertyId, IUIAutomationElement8,
    HeadingLevel7, ProviderOptions_UseClientCoordinates,
    UIA_BulletStyleAttributeId, UIA_WindowIsTopmostPropertyId,
    ZoomUnit_SmallDecrement, UIA_FontNameAttributeId,
    IUIAutomationTextChildPattern,
    NotificationProcessing_ImportantAll, UIA_CaretBidiModeAttributeId,
    AnnotationType_Author, StructureChangeType_ChildrenInvalidated,
    UIA_IsEnabledPropertyId, UIA_ItemTypePropertyId, Library,
    UIA_DataGridControlTypeId, UIA_IsDragPatternAvailablePropertyId,
    UIA_SelectionItem_ElementSelectedEventId,
    AnnotationType_FormulaError, UIA_SizePropertyId,
    IUIAutomationElement4, UIA_HasKeyboardFocusPropertyId,
    PropertyConditionFlags_MatchSubstring,
    ProviderOptions_NonClientAreaProvider,
    IUIAutomationGridItemPattern, CoalesceEventsOptions_Enabled,
    UIA_LevelPropertyId, CUIAutomation8, UIA_HeaderItemControlTypeId,
    UIA_SelectionItemSelectionContainerPropertyId,
    UIA_AutomationFocusChangedEventId, UIA_LineSpacingAttributeId,
    UIA_IsTableItemPatternAvailablePropertyId,
    UIA_AnnotationTypesAttributeId, AnnotationType_ConflictingChange,
    ScrollAmount_SmallDecrement, SynchronizedInputType_KeyUp,
    TreeScope_Ancestors, UIA_DragDropEffectPropertyId,
    UIA_SizeOfSetPropertyId, UIA_ListItemControlTypeId,
    RowOrColumnMajor_Indeterminate, HeadingLevel4,
    IUIAutomationAnnotationPattern,
    UIA_IsLegacyIAccessiblePatternAvailablePropertyId,
    ToggleState_Indeterminate, UIA_LegacyIAccessibleChildIdPropertyId,
    TreeTraversalOptions_Default, NavigateDirection_Parent,
    IUIAutomationItemContainerPattern, TextUnit_Line,
    UIA_NotificationEventId, UIA_MarginTopAttributeId, HeadingLevel1,
    IUIAutomationChangesEventHandler, AnnotationType_ExternalChange,
    DockPosition_None, UIA_SelectionSelectionPropertyId,
    StyleId_Heading2, StyleId_Quote, NavigateDirection_FirstChild,
    UIA_Drag_DragStartEventId, UIA_IsTogglePatternAvailablePropertyId,
    UIA_GridItemContainingGridPropertyId,
    ProviderOptions_ClientSideProvider,
    UIA_DropTargetDropTargetEffectPropertyId,
    IUIAutomationTextRangeArray, UIA_MenuControlTypeId,
    UIA_GridItemPatternId, UIA_Selection2LastSelectedItemPropertyId,
    UIA_GridItemColumnPropertyId, UIA_RangeValueValuePropertyId,
    UIA_Selection2FirstSelectedItemPropertyId,
    UIA_StyleNameAttributeId, UIA_IsControlElementPropertyId,
    StyleId_Heading8, IUIAutomationSpreadsheetPattern,
    TreeTraversalOptions_PostOrder, UIA_GridItemColumnSpanPropertyId,
    UIA_StylesStyleIdPropertyId, UIA_Invoke_InvokedEventId,
    UIA_DataItemControlTypeId, AnnotationType_SpellingError,
    UIA_InputDiscardedEventId, AnnotationType_TrackChanges,
    UIA_GridItemRowSpanPropertyId,
    IUIAutomationSynchronizedInputPattern,
    UIA_ToggleToggleStatePropertyId, IUIAutomationInvokePattern,
    AnnotationType_FormatChange,
    UIA_MultipleViewSupportedViewsPropertyId,
    IUIAutomationTextEditPattern, UIA_ScrollPatternId,
    UIA_IsInvokePatternAvailablePropertyId,
    UIA_AriaPropertiesPropertyId, TextUnit_Format,
    UIA_IsHiddenAttributeId, _lcid, UIA_StylesShapePropertyId,
    UIA_SelectionItem_ElementRemovedFromSelectionEventId, HRESULT,
    UIA_IsOffscreenPropertyId, IUIAutomationTextRange2,
    UIA_MultipleViewCurrentViewPropertyId,
    UIA_AnnotationObjectsPropertyId, DockPosition_Fill,
    UIA_AsyncContentLoadedEventId, IDispatch,
    UIA_SelectionIsSelectionRequiredPropertyId,
    AnnotationType_UnsyncedChange, NotificationKind_Other,
    UIA_LocalizedControlTypePropertyId, UIA_GridItemRowPropertyId,
    UIA_DropTarget_DragEnterEventId,
    NotificationProcessing_ImportantCurrentThenMostRecent,
    WindowVisualState_Normal, SynchronizedInputType_LeftMouseUp,
    UIA_ScrollVerticalScrollPercentPropertyId,
    UIA_RangeValueSmallChangePropertyId, UIA_GroupControlTypeId,
    UIA_IsWindowPatternAvailablePropertyId,
    UIA_Drag_DragCancelEventId, UIA_RuntimeIdPropertyId,
    UIA_DescribedByPropertyId, IUIAutomationValuePattern,
    UIA_MenuOpenedEventId, ZoomUnit_SmallIncrement,
    UIA_TextChildPatternId, IUIAutomationAndCondition,
    UIA_TableRowHeadersPropertyId, IUIAutomationElement,
    ConnectionRecoveryBehaviorOptions_Enabled,
    UIA_RangeValuePatternId, UIA_MenuModeStartEventId,
    ExpandCollapseState_Expanded, UIA_SelectionActiveEndAttributeId,
    UIA_ValueValuePropertyId,
    IUIAutomationStructureChangedEventHandler, UIA_TextEditPatternId,
    UIA_CenterPointPropertyId, UIA_Transform2ZoomMaximumPropertyId,
    UIA_FlowsFromPropertyId, SupportedTextSelection_Single,
    ToggleState_On, UIA_FontWeightAttributeId, AnnotationType_Unknown,
    ExpandCollapseState_Collapsed, UIA_ToolBarControlTypeId,
    UIA_StyleIdAttributeId, UIA_LegacyIAccessibleValuePropertyId,
    TextEditChangeType_CompositionFinalized, UIA_SummaryChangeId,
    IUIAutomationRangeValuePattern,
    UIA_OptimizeForVisualContentPropertyId, HeadingLevel8,
    UIA_WindowCanMinimizePropertyId,
    UIA_IsAnnotationPatternAvailablePropertyId, ZoomUnit_NoAmount,
    UIA_CheckBoxControlTypeId, UIA_DockDockPositionPropertyId,
    IUIAutomationTablePattern, UIA_ClassNamePropertyId,
    WindowInteractionState_NotResponding, IUIAutomationTextPattern2,
    IUIAutomationEventHandler, UIA_ComboBoxControlTypeId,
    NotificationKind_ActionCompleted, UIA_ToolTipOpenedEventId,
    UIA_TabsAttributeId, UIA_SpreadsheetItemPatternId,
    UIA_FlowsToPropertyId, StructureChangeType_ChildrenBulkRemoved,
    UIA_ScrollItemPatternId, UIA_ThumbControlTypeId,
    AnnotationType_DataValidationError, UIA_DragPatternId,
    HeadingLevel6, UIA_HyperlinkControlTypeId,
    ProviderOptions_ProviderOwnsSetFocus, AnnotationType_Footer,
    COMMETHOD, IUIAutomationCacheRequest, IUIAutomationElementArray,
    ProviderOptions_UseComThreading, UIA_AnnotationTargetPropertyId,
    UIA_StylesExtendedPropertiesPropertyId, DockPosition_Right,
    UIA_MarginTrailingAttributeId, UIA_OutlineThicknessPropertyId,
    UIA_MultipleViewPatternId, UIA_SayAsInterpretAsAttributeId,
    UIA_DragIsGrabbedPropertyId, Off, UIA_PaneControlTypeId,
    UIA_DragDropEffectsPropertyId, TreeScope_Element,
    IUIAutomationElement2, UIA_SpreadsheetPatternId,
    UIA_ControlTypePropertyId, AutomationElementMode_None,
    UIA_IsTransformPattern2AvailablePropertyId, ScrollAmount_NoAmount,
    UIA_IsSubscriptAttributeId, CoalesceEventsOptions_Disabled,
    UIA_ProviderDescriptionPropertyId, UIA_FillTypePropertyId,
    UIA_IsDockPatternAvailablePropertyId,
    IUIAutomationSpreadsheetItemPattern, IUIAutomationTextRange3,
    TextUnit_Page, UIA_StrikethroughStyleAttributeId,
    IUIAutomationGridPattern, UIA_GridColumnCountPropertyId,
    UIA_AnnotationDateTimePropertyId, IUIAutomationSelectionPattern2,
    UIA_RadioButtonControlTypeId, StyleId_Heading1,
    UIA_LegacyIAccessiblePatternId,
    WindowInteractionState_ReadyForUserInteraction,
    UIA_AnimationStyleAttributeId, UIA_LayoutInvalidatedEventId,
    IUIAutomationTreeWalker, UIA_GridRowCountPropertyId,
    UIA_ClickablePointPropertyId, UIA_TransformPattern2Id,
    UIA_ExpandCollapseExpandCollapseStatePropertyId,
    WindowInteractionState_Running, UIA_MenuItemControlTypeId,
    UIA_AcceleratorKeyPropertyId, UIA_FullDescriptionPropertyId,
    UIA_CustomLandmarkTypeId, UIA_SystemAlertEventId,
    StyleId_Heading7, UIA_FillColorPropertyId,
    UIA_WindowIsModalPropertyId,
    IUIAutomationLegacyIAccessiblePattern,
    IUIAutomationTextEditTextChangedEventHandler,
    UIA_RangeValueLargeChangePropertyId,
    UIA_IsSynchronizedInputPatternAvailablePropertyId,
    UIA_LabeledByPropertyId, AnnotationType_Header, TextUnit_Document,
    UIA_HorizontalTextAlignmentAttributeId,
    UIA_IsVirtualizedItemPatternAvailablePropertyId,
    UIA_IsActiveAttributeId, UIA_DragGrabbedItemsPropertyId,
    IUIAutomationScrollItemPattern, IAccessible,
    UIA_IsItalicAttributeId, TextEditChangeType_Composition,
    TextPatternRangeEndpoint_End, Assertive,
    UIA_VisualEffectsPropertyId,
    TreeTraversalOptions_LastToFirstOrder, UIA_ToolTipControlTypeId,
    UIA_AccessKeyPropertyId,
    UIA_IsTransformPatternAvailablePropertyId,
    UIA_IsValuePatternAvailablePropertyId, UIA_FormLandmarkTypeId,
    IUIAutomation5, UIA_LandmarkTypePropertyId,
    UIA_SplitButtonControlTypeId,
    UIA_WindowWindowVisualStatePropertyId,
    UIA_IsContentElementPropertyId, IUIAutomationElement6,
    UIA_WindowPatternId, UIA_IsMultipleViewPatternAvailablePropertyId,
    UIA_StrikethroughColorAttributeId,
    UIA_WindowWindowInteractionStatePropertyId,
    IUIAutomationPropertyCondition, UIA_ObjectModelPatternId,
    UIA_IsCustomNavigationPatternAvailablePropertyId,
    IUIAutomationTableItemPattern, UIA_ExpandCollapsePatternId,
    AnnotationType_Comment, DockPosition_Top,
    IUIAutomationWindowPattern, BSTR, ZoomUnit_LargeDecrement,
    UIA_HelpTextPropertyId, CoClass, OrientationType_Vertical,
    HeadingLevel2, UIA_LinkAttributeId, UIA_IsDialogPropertyId,
    _midlSAFEARRAY, UIA_Text_TextChangedEventId,
    IUIAutomationNotCondition, UIA_DropTarget_DragLeaveEventId,
    IUIAutomationPropertyChangedEventHandler, StyleId_Custom,
    IUIAutomationScrollPattern, StructureChangeType_ChildrenReordered,
    UIA_UnderlineColorAttributeId, UIA_RangeValueIsReadOnlyPropertyId,
    UIA_LegacyIAccessibleDescriptionPropertyId,
    PropertyConditionFlags_IgnoreCase,
    IUIAutomationCustomNavigationPattern,
    TextPatternRangeEndpoint_Start, UIA_OverlineStyleAttributeId,
    TreeScope_None, UIA_IsTextChildPatternAvailablePropertyId,
    WindowInteractionState_BlockedByModalWindow,
    ProviderOptions_OverrideProvider,
    UIA_TableItemColumnHeaderItemsPropertyId, UIA_DockPatternId,
    AutomationElementMode_Full, NotificationProcessing_MostRecent,
    UIA_SynchronizedInputPatternId, PropertyConditionFlags_None,
    _check_version, UIA_AutomationIdPropertyId,
    UIA_IsTablePatternAvailablePropertyId, UIA_ToolTipClosedEventId,
    NotificationKind_ActionAborted, UIA_LiveSettingPropertyId,
    IUIAutomation6, UIA_IndentationLeadingAttributeId,
    UIA_TextControlTypeId, TextEditChangeType_AutoCorrect,
    UIA_RangeValueMaximumPropertyId, UIA_InvokePatternId,
    UIA_IsExpandCollapsePatternAvailablePropertyId,
    SynchronizedInputType_RightMouseUp,
    UIA_AnnotationAnnotationTypeIdPropertyId, UiaChangeInfo,
    UIA_AnnotationTypesPropertyId, tagPOINT, ToggleState_Off,
    HeadingLevel_None, UIA_ScrollVerticallyScrollablePropertyId,
    IUIAutomationSelectionPattern, UIA_SearchLandmarkTypeId,
    CUIAutomation, UIA_LegacyIAccessibleDefaultActionPropertyId,
    UIA_IsDataValidForFormPropertyId,
    UIA_AutomationPropertyChangedEventId, ScrollAmount_LargeIncrement,
    UIA_HeaderControlTypeId,
    UIA_LegacyIAccessibleKeyboardShortcutPropertyId,
    UIA_HostedFragmentRootsInvalidatedEventId, UIA_CultureAttributeId,
    UIA_InputReachedTargetEventId, UIA_IndentationTrailingAttributeId,
    UIA_ItemStatusPropertyId, HeadingLevel5, TextUnit_Paragraph,
    WindowVisualState_Maximized, AnnotationType_Mathematics,
    ScrollAmount_SmallIncrement, UIA_StylesFillColorPropertyId,
    NavigateDirection_LastChild, IUIAutomationTextRange,
    UIA_NamePropertyId, StructureChangeType_ChildAdded,
    IUIAutomationNotificationEventHandler, UIA_RotationPropertyId,
    IUIAutomationProxyFactoryEntry, UIA_Transform2CanZoomPropertyId,
    UIA_IndentationFirstLineAttributeId, UIA_IsReadOnlyAttributeId,
    StyleId_Heading4, VARIANT, UIA_DropTarget_DroppedEventId,
    UIA_EditControlTypeId, dispid, IUIAutomationProxyFactory,
    UIA_CustomNavigationPatternId, IUIAutomationTogglePattern,
    UIA_InputReachedOtherElementEventId, OrientationType_None,
    UIA_RangeValueMinimumPropertyId, UIA_BoundingRectanglePropertyId,
    UIA_BeforeParagraphSpacingAttributeId, UIA_TreeControlTypeId,
    WindowVisualState_Minimized, IUIAutomationOrCondition,
    UIA_BackgroundColorAttributeId,
    UIA_ActiveTextPositionChangedEventId, UIA_FrameworkIdPropertyId,
    SynchronizedInputType_KeyDown, TextEditChangeType_None,
    StyleId_Heading6, UIA_IsSelectionItemPatternAvailablePropertyId,
    UIA_MainLandmarkTypeId, UIA_MenuModeEndEventId, ExtendedProperty,
    UIA_IsTextPatternAvailablePropertyId,
    UIA_UnderlineStyleAttributeId,
    ProviderOptions_RefuseNonClientSupport,
    UIA_Window_WindowClosedEventId, UIA_PositionInSetPropertyId,
    UIA_AriaRolePropertyId, UIA_ForegroundColorAttributeId,
    UIA_ScrollHorizontallyScrollablePropertyId,
    UIA_Transform2ZoomLevelPropertyId, IUIAutomation4,
    WindowInteractionState_Closing, UIA_SliderControlTypeId,
    UIA_LegacyIAccessibleStatePropertyId, UIA_MenuBarControlTypeId,
    IUIAutomationDockPattern, StyleId_NumberedList,
    UIA_IsSuperscriptAttributeId, AnnotationType_DeletionChange,
    UIA_IsStylesPatternAvailablePropertyId,
    UIA_LegacyIAccessibleSelectionPropertyId,
    UIA_StatusBarControlTypeId, AnnotationType_Footnote, Polite,
    IUIAutomationCondition, StyleId_Heading9,
    UIA_SelectionItemIsSelectedPropertyId,
    NotificationProcessing_ImportantMostRecent, UIA_ChangesEventId,
    UIA_Drag_DragCompleteEventId, TextEditChangeType_AutoComplete,
    UIA_IsKeyboardFocusablePropertyId,
    UIA_LegacyIAccessibleNamePropertyId, UIA_TitleBarControlTypeId,
    NotificationProcessing_All, UIA_TogglePatternId,
    UIA_TransformCanMovePropertyId, IUIAutomation3,
    IUIAutomationElement9, AnnotationType_MoveChange,
    StyleId_Heading3, UIA_SpreadsheetItemAnnotationTypesPropertyId,
    UIA_WindowCanMaximizePropertyId, UIA_TablePatternId,
    RowOrColumnMajor_ColumnMajor, ExpandCollapseState_LeafNode,
    UIA_IsRequiredForFormPropertyId, IUIAutomationTransformPattern,
    UIA_TabItemControlTypeId, AnnotationType_Highlighted,
    TreeScope_Descendants, ProviderOptions_ServerSideProvider,
    IUnknown, UIA_TableControlTypeId,
    UIA_ScrollHorizontalScrollPercentPropertyId,
    UIA_ScrollBarControlTypeId, ExpandCollapseState_PartiallyExpanded,
    ZoomUnit_LargeIncrement,
    UIA_IsDropTargetPatternAvailablePropertyId,
    IUIAutomationTransformPattern2,
    SynchronizedInputType_LeftMouseDown, UIA_VirtualizedItemPatternId,
    UIA_TextPattern2Id, StyleId_Title, UIA_StylesStyleNamePropertyId,
    UIA_Transform2ZoomMinimumPropertyId,
    ConnectionRecoveryBehaviorOptions_Disabled, GUID,
    UIA_TableItemPatternId, UIA_ScrollHorizontalViewSizePropertyId,
    AnnotationType_Sensitive, UIA_StylesPatternId,
    UIA_TabControlTypeId, UIA_SelectionPatternId,
    IUIAutomationBoolCondition, IUIAutomationElement3,
    UIA_MarginLeadingAttributeId, DockPosition_Bottom,
    UIA_Window_WindowOpenedEventId, AnnotationType_Endnote,
    UIA_FontSizeAttributeId, ScrollAmount_LargeDecrement,
    UIA_TreeItemControlTypeId,
    UIA_SelectionCanSelectMultiplePropertyId,
    UIA_AnnotationAnnotationTypeNamePropertyId,
    UIA_AnnotationAuthorPropertyId, IUIAutomationElement5,
    AnnotationType_GrammarError, StyleId_Subtitle,
    SupportedTextSelection_Multiple, UIA_OutlineStylesAttributeId,
    UIA_Selection_InvalidatedEventId,
    UIA_IsItemContainerPatternAvailablePropertyId,
    UIA_IsSelectionPatternAvailablePropertyId,
    UIA_MarginBottomAttributeId, StyleId_Normal,
    UIA_LiveRegionChangedEventId,
    UIA_IsScrollPatternAvailablePropertyId, IUIAutomation,
    StyleId_Emphasis, UIA_TransformPatternId,
    UIA_TableColumnHeadersPropertyId,
    ProviderOptions_HasNativeIAccessible,
    IUIAutomationActiveTextPositionChangedEventHandler,
    UIA_GridPatternId, UIA_DropTargetPatternId,
    UIA_IsTextPattern2AvailablePropertyId, IUIAutomationStylesPattern,
    AnnotationType_CircularReferenceError,
    NavigateDirection_PreviousSibling, UIA_CulturePropertyId,
    UIA_IsGridPatternAvailablePropertyId, RowOrColumnMajor_RowMajor,
    UIA_AnnotationObjectsAttributeId, IUIAutomation2,
    IUIAutomationSelectionItemPattern,
    UIA_NativeWindowHandlePropertyId, typelib_path,
    UIA_OrientationPropertyId, UIA_ControllerForPropertyId,
    UIA_IsSpreadsheetItemPatternAvailablePropertyId,
    UIA_StylesFillPatternColorPropertyId, UIA_IsPeripheralPropertyId,
    NotificationProcessing_CurrentThenMostRecent,
    UIA_SpreadsheetItemAnnotationObjectsPropertyId,
    UIA_IsTextEditPatternAvailablePropertyId,
    UIA_AfterParagraphSpacingAttributeId,
    IUIAutomationFocusChangedEventHandler, IUIAutomationTextPattern,
    UIA_SeparatorControlTypeId, UIA_ScrollVerticalViewSizePropertyId,
    UIA_SelectionPattern2Id, IUIAutomationVirtualizedItemPattern,
    SynchronizedInputType_RightMouseDown
)


class OrientationType(IntFlag):
    OrientationType_None = 0
    OrientationType_Horizontal = 1
    OrientationType_Vertical = 2


class ProviderOptions(IntFlag):
    ProviderOptions_ClientSideProvider = 1
    ProviderOptions_ServerSideProvider = 2
    ProviderOptions_NonClientAreaProvider = 4
    ProviderOptions_OverrideProvider = 8
    ProviderOptions_ProviderOwnsSetFocus = 16
    ProviderOptions_UseComThreading = 32
    ProviderOptions_RefuseNonClientSupport = 64
    ProviderOptions_HasNativeIAccessible = 128
    ProviderOptions_UseClientCoordinates = 256


class ZoomUnit(IntFlag):
    ZoomUnit_NoAmount = 0
    ZoomUnit_LargeDecrement = 1
    ZoomUnit_SmallDecrement = 2
    ZoomUnit_LargeIncrement = 3
    ZoomUnit_SmallIncrement = 4


class TextPatternRangeEndpoint(IntFlag):
    TextPatternRangeEndpoint_Start = 0
    TextPatternRangeEndpoint_End = 1


class TextUnit(IntFlag):
    TextUnit_Character = 0
    TextUnit_Format = 1
    TextUnit_Word = 2
    TextUnit_Line = 3
    TextUnit_Paragraph = 4
    TextUnit_Page = 5
    TextUnit_Document = 6


class WindowVisualState(IntFlag):
    WindowVisualState_Normal = 0
    WindowVisualState_Maximized = 1
    WindowVisualState_Minimized = 2


class ScrollAmount(IntFlag):
    ScrollAmount_LargeDecrement = 0
    ScrollAmount_SmallDecrement = 1
    ScrollAmount_NoAmount = 2
    ScrollAmount_LargeIncrement = 3
    ScrollAmount_SmallIncrement = 4


class WindowInteractionState(IntFlag):
    WindowInteractionState_Running = 0
    WindowInteractionState_Closing = 1
    WindowInteractionState_ReadyForUserInteraction = 2
    WindowInteractionState_BlockedByModalWindow = 3
    WindowInteractionState_NotResponding = 4


class TreeScope(IntFlag):
    TreeScope_None = 0
    TreeScope_Element = 1
    TreeScope_Children = 2
    TreeScope_Descendants = 4
    TreeScope_Parent = 8
    TreeScope_Ancestors = 16
    TreeScope_Subtree = 7


class LiveSetting(IntFlag):
    Off = 0
    Polite = 1
    Assertive = 2


class TextEditChangeType(IntFlag):
    TextEditChangeType_None = 0
    TextEditChangeType_AutoCorrect = 1
    TextEditChangeType_Composition = 2
    TextEditChangeType_CompositionFinalized = 3
    TextEditChangeType_AutoComplete = 4


class PropertyConditionFlags(IntFlag):
    PropertyConditionFlags_None = 0
    PropertyConditionFlags_IgnoreCase = 1
    PropertyConditionFlags_MatchSubstring = 2


class AutomationElementMode(IntFlag):
    AutomationElementMode_None = 0
    AutomationElementMode_Full = 1


class SynchronizedInputType(IntFlag):
    SynchronizedInputType_KeyUp = 1
    SynchronizedInputType_KeyDown = 2
    SynchronizedInputType_LeftMouseUp = 4
    SynchronizedInputType_LeftMouseDown = 8
    SynchronizedInputType_RightMouseUp = 16
    SynchronizedInputType_RightMouseDown = 32


class RowOrColumnMajor(IntFlag):
    RowOrColumnMajor_RowMajor = 0
    RowOrColumnMajor_ColumnMajor = 1
    RowOrColumnMajor_Indeterminate = 2


class SupportedTextSelection(IntFlag):
    SupportedTextSelection_None = 0
    SupportedTextSelection_Single = 1
    SupportedTextSelection_Multiple = 2


class TreeTraversalOptions(IntFlag):
    TreeTraversalOptions_Default = 0
    TreeTraversalOptions_PostOrder = 1
    TreeTraversalOptions_LastToFirstOrder = 2


class ToggleState(IntFlag):
    ToggleState_Off = 0
    ToggleState_On = 1
    ToggleState_Indeterminate = 2


class NavigateDirection(IntFlag):
    NavigateDirection_Parent = 0
    NavigateDirection_NextSibling = 1
    NavigateDirection_PreviousSibling = 2
    NavigateDirection_FirstChild = 3
    NavigateDirection_LastChild = 4


class StructureChangeType(IntFlag):
    StructureChangeType_ChildAdded = 0
    StructureChangeType_ChildRemoved = 1
    StructureChangeType_ChildrenInvalidated = 2
    StructureChangeType_ChildrenBulkAdded = 3
    StructureChangeType_ChildrenBulkRemoved = 4
    StructureChangeType_ChildrenReordered = 5


class NotificationKind(IntFlag):
    NotificationKind_ItemAdded = 0
    NotificationKind_ItemRemoved = 1
    NotificationKind_ActionCompleted = 2
    NotificationKind_ActionAborted = 3
    NotificationKind_Other = 4


class NotificationProcessing(IntFlag):
    NotificationProcessing_ImportantAll = 0
    NotificationProcessing_ImportantMostRecent = 1
    NotificationProcessing_All = 2
    NotificationProcessing_MostRecent = 3
    NotificationProcessing_CurrentThenMostRecent = 4
    NotificationProcessing_ImportantCurrentThenMostRecent = 5


class DockPosition(IntFlag):
    DockPosition_Top = 0
    DockPosition_Left = 1
    DockPosition_Bottom = 2
    DockPosition_Right = 3
    DockPosition_Fill = 4
    DockPosition_None = 5


class ExpandCollapseState(IntFlag):
    ExpandCollapseState_Collapsed = 0
    ExpandCollapseState_Expanded = 1
    ExpandCollapseState_PartiallyExpanded = 2
    ExpandCollapseState_LeafNode = 3


class ConnectionRecoveryBehaviorOptions(IntFlag):
    ConnectionRecoveryBehaviorOptions_Disabled = 0
    ConnectionRecoveryBehaviorOptions_Enabled = 1


class CoalesceEventsOptions(IntFlag):
    CoalesceEventsOptions_Disabled = 0
    CoalesceEventsOptions_Enabled = 1


__all__ = [
    'UIA_ValueIsReadOnlyPropertyId', 'UIA_CapStyleAttributeId',
    'UIA_ItemContainerPatternId', 'UIA_CalendarControlTypeId',
    'UIA_StylesFillPatternStylePropertyId',
    'UIA_CaretPositionAttributeId',
    'UIA_TableRowOrColumnMajorPropertyId', 'UIA_WindowControlTypeId',
    'TreeScope_Subtree', 'UIA_DocumentControlTypeId',
    'NotificationKind_ItemAdded', 'UIA_MenuClosedEventId',
    'UIA_LegacyIAccessibleHelpPropertyId', 'UIA_ListControlTypeId',
    'UIA_LegacyIAccessibleRolePropertyId',
    'AnnotationType_InsertionChange',
    'StructureChangeType_ChildrenBulkAdded',
    'UIA_TableItemRowHeaderItemsPropertyId', 'HeadingLevel9',
    'IRawElementProviderSimple', 'ConnectionRecoveryBehaviorOptions',
    'UIA_SemanticZoomControlTypeId', 'TextUnit_Word',
    'UIA_SelectionItemPatternId', 'UIA_SayAsInterpretAsMetadataId',
    'TextUnit', 'SupportedTextSelection_None',
    'UIA_IsSpreadsheetPatternAvailablePropertyId', 'ProviderOptions',
    'IUIAutomationElement7', 'UIA_AnnotationPatternId',
    'UIA_Text_TextSelectionChangedEventId',
    'UIA_TextEdit_ConversionTargetChangedEventId',
    'DockPosition_Left', 'UIA_IsGridItemPatternAvailablePropertyId',
    'UIA_ButtonControlTypeId', 'StyleId_BulletedList',
    'AnnotationType_EditingLockedChange', 'UIA_SpinnerControlTypeId',
    'WindowInteractionState',
    'UIA_Selection2CurrentSelectedItemPropertyId',
    'UIA_TextPatternId', 'UIA_LocalizedLandmarkTypePropertyId',
    'IUIAutomationDropTargetPattern',
    'UIA_IsRangeValuePatternAvailablePropertyId',
    'UIA_OutlineColorPropertyId', 'UIA_NavigationLandmarkTypeId',
    'OrientationType_Horizontal', 'NotificationKind_ItemRemoved',
    'UIA_StructureChangedEventId', 'IUIAutomationDragPattern',
    'StructureChangeType_ChildRemoved', 'UIA_ProcessIdPropertyId',
    'UIA_CustomControlTypeId', 'SynchronizedInputType',
    'TextUnit_Character', 'IUIAutomationProxyFactoryMapping',
    'AnnotationType_AdvancedProofingIssue',
    'IUIAutomationObjectModelPattern',
    'UIA_TextEdit_TextChangedEventId', 'UIA_ImageControlTypeId',
    'StyleId_Heading5', 'IUIAutomationEventHandlerGroup',
    'UIA_IsScrollItemPatternAvailablePropertyId',
    'UIA_SpreadsheetItemFormulaPropertyId', 'HeadingLevel3',
    'UIA_HeadingLevelPropertyId', 'UIA_TransformCanRotatePropertyId',
    'UIA_SelectionItem_ElementAddedToSelectionEventId',
    'TreeScope_Parent', 'UIA_IsObjectModelPatternAvailablePropertyId',
    'IUIAutomationExpandCollapsePattern', 'TreeScope_Children',
    'UIA_IsSelectionPattern2AvailablePropertyId',
    'UIA_IsPasswordPropertyId', 'UIA_OverlineColorAttributeId',
    'UIA_AppBarControlTypeId', 'UIA_Selection2ItemCountPropertyId',
    'NavigateDirection_NextSibling', 'UIA_ValuePatternId',
    'IUIAutomationMultipleViewPattern', 'NotificationProcessing',
    'UIA_ProgressBarControlTypeId',
    'UIA_DropTargetDropTargetEffectsPropertyId',
    'UIA_TextFlowDirectionsAttributeId',
    'UIA_TransformCanResizePropertyId', 'IUIAutomationElement8',
    'HeadingLevel7', 'ProviderOptions_UseClientCoordinates',
    'UIA_BulletStyleAttributeId', 'UIA_WindowIsTopmostPropertyId',
    'ZoomUnit_SmallDecrement', 'UIA_FontNameAttributeId',
    'IUIAutomationTextChildPattern',
    'NotificationProcessing_ImportantAll',
    'UIA_CaretBidiModeAttributeId', 'AnnotationType_Author',
    'StructureChangeType_ChildrenInvalidated',
    'UIA_IsEnabledPropertyId', 'UIA_ItemTypePropertyId', 'Library',
    'UIA_DataGridControlTypeId',
    'UIA_IsDragPatternAvailablePropertyId',
    'UIA_SelectionItem_ElementSelectedEventId',
    'AnnotationType_FormulaError', 'UIA_SizePropertyId',
    'IUIAutomationElement4', 'UIA_HasKeyboardFocusPropertyId',
    'PropertyConditionFlags_MatchSubstring',
    'ProviderOptions_NonClientAreaProvider',
    'IUIAutomationGridItemPattern', 'CoalesceEventsOptions_Enabled',
    'UIA_LevelPropertyId', 'CUIAutomation8',
    'UIA_HeaderItemControlTypeId',
    'UIA_SelectionItemSelectionContainerPropertyId',
    'UIA_AutomationFocusChangedEventId', 'UIA_LineSpacingAttributeId',
    'UIA_IsTableItemPatternAvailablePropertyId',
    'UIA_AnnotationTypesAttributeId',
    'AnnotationType_ConflictingChange', 'ScrollAmount_SmallDecrement',
    'SynchronizedInputType_KeyUp', 'TreeScope_Ancestors',
    'UIA_DragDropEffectPropertyId', 'UIA_SizeOfSetPropertyId',
    'UIA_ListItemControlTypeId', 'RowOrColumnMajor_Indeterminate',
    'HeadingLevel4', 'IUIAutomationAnnotationPattern',
    'UIA_IsLegacyIAccessiblePatternAvailablePropertyId',
    'ToggleState_Indeterminate',
    'UIA_LegacyIAccessibleChildIdPropertyId',
    'TreeTraversalOptions_Default', 'NavigateDirection_Parent',
    'IUIAutomationItemContainerPattern', 'TextUnit_Line',
    'UIA_NotificationEventId', 'UIA_MarginTopAttributeId',
    'HeadingLevel1', 'IUIAutomationChangesEventHandler',
    'AnnotationType_ExternalChange', 'DockPosition_None',
    'UIA_SelectionSelectionPropertyId', 'StyleId_Heading2',
    'StyleId_Quote', 'TextEditChangeType',
    'NavigateDirection_FirstChild', 'UIA_Drag_DragStartEventId',
    'UIA_IsTogglePatternAvailablePropertyId',
    'UIA_GridItemContainingGridPropertyId', 'RowOrColumnMajor',
    'ProviderOptions_ClientSideProvider',
    'UIA_DropTargetDropTargetEffectPropertyId', 'ToggleState',
    'IUIAutomationTextRangeArray', 'UIA_MenuControlTypeId',
    'UIA_GridItemPatternId',
    'UIA_Selection2LastSelectedItemPropertyId',
    'UIA_GridItemColumnPropertyId', 'UIA_RangeValueValuePropertyId',
    'UIA_Selection2FirstSelectedItemPropertyId',
    'UIA_StyleNameAttributeId', 'UIA_IsControlElementPropertyId',
    'StyleId_Heading8', 'IUIAutomationSpreadsheetPattern',
    'TreeTraversalOptions_PostOrder',
    'UIA_GridItemColumnSpanPropertyId', 'UIA_StylesStyleIdPropertyId',
    'UIA_Invoke_InvokedEventId', 'UIA_DataItemControlTypeId',
    'AnnotationType_SpellingError', 'UIA_InputDiscardedEventId',
    'AnnotationType_TrackChanges', 'UIA_GridItemRowSpanPropertyId',
    'IUIAutomationSynchronizedInputPattern',
    'UIA_ToggleToggleStatePropertyId', 'IUIAutomationInvokePattern',
    'AnnotationType_FormatChange',
    'UIA_MultipleViewSupportedViewsPropertyId',
    'IUIAutomationTextEditPattern', 'UIA_ScrollPatternId',
    'UIA_IsInvokePatternAvailablePropertyId',
    'UIA_AriaPropertiesPropertyId', 'TextUnit_Format',
    'UIA_IsHiddenAttributeId', 'UIA_StylesShapePropertyId',
    'UIA_SelectionItem_ElementRemovedFromSelectionEventId',
    'UIA_IsOffscreenPropertyId', 'IUIAutomationTextRange2',
    'UIA_MultipleViewCurrentViewPropertyId',
    'UIA_AnnotationObjectsPropertyId', 'DockPosition_Fill',
    'UIA_AsyncContentLoadedEventId',
    'UIA_SelectionIsSelectionRequiredPropertyId',
    'PropertyConditionFlags', 'AnnotationType_UnsyncedChange',
    'NotificationKind_Other', 'UIA_LocalizedControlTypePropertyId',
    'UIA_GridItemRowPropertyId', 'UIA_DropTarget_DragEnterEventId',
    'NotificationProcessing_ImportantCurrentThenMostRecent',
    'WindowVisualState_Normal', 'SynchronizedInputType_LeftMouseUp',
    'UIA_ScrollVerticalScrollPercentPropertyId',
    'UIA_RangeValueSmallChangePropertyId', 'UIA_GroupControlTypeId',
    'UIA_IsWindowPatternAvailablePropertyId',
    'UIA_Drag_DragCancelEventId', 'UIA_RuntimeIdPropertyId',
    'UIA_DescribedByPropertyId', 'IUIAutomationValuePattern',
    'UIA_MenuOpenedEventId', 'ZoomUnit_SmallIncrement',
    'UIA_TextChildPatternId', 'IUIAutomationAndCondition',
    'UIA_TableRowHeadersPropertyId', 'IUIAutomationElement',
    'ConnectionRecoveryBehaviorOptions_Enabled',
    'UIA_RangeValuePatternId', 'UIA_MenuModeStartEventId',
    'ExpandCollapseState_Expanded',
    'UIA_SelectionActiveEndAttributeId', 'UIA_ValueValuePropertyId',
    'IUIAutomationStructureChangedEventHandler',
    'UIA_TextEditPatternId', 'UIA_CenterPointPropertyId',
    'UIA_Transform2ZoomMaximumPropertyId', 'UIA_FlowsFromPropertyId',
    'SupportedTextSelection_Single', 'ToggleState_On',
    'UIA_FontWeightAttributeId', 'AnnotationType_Unknown',
    'ExpandCollapseState_Collapsed', 'UIA_ToolBarControlTypeId',
    'UIA_StyleIdAttributeId', 'UIA_LegacyIAccessibleValuePropertyId',
    'TextEditChangeType_CompositionFinalized', 'UIA_SummaryChangeId',
    'IUIAutomationRangeValuePattern',
    'UIA_OptimizeForVisualContentPropertyId', 'HeadingLevel8',
    'UIA_WindowCanMinimizePropertyId',
    'UIA_IsAnnotationPatternAvailablePropertyId', 'ZoomUnit_NoAmount',
    'TreeScope', 'UIA_CheckBoxControlTypeId',
    'UIA_DockDockPositionPropertyId', 'IUIAutomationTablePattern',
    'UIA_ClassNamePropertyId', 'WindowInteractionState_NotResponding',
    'NavigateDirection', 'IUIAutomationTextPattern2',
    'IUIAutomationEventHandler', 'UIA_ComboBoxControlTypeId',
    'NotificationKind_ActionCompleted', 'UIA_ToolTipOpenedEventId',
    'UIA_TabsAttributeId', 'UIA_SpreadsheetItemPatternId',
    'UIA_FlowsToPropertyId',
    'StructureChangeType_ChildrenBulkRemoved',
    'UIA_ScrollItemPatternId', 'UIA_ThumbControlTypeId',
    'WindowVisualState', 'AnnotationType_DataValidationError',
    'UIA_DragPatternId', 'HeadingLevel6',
    'UIA_HyperlinkControlTypeId',
    'ProviderOptions_ProviderOwnsSetFocus', 'AnnotationType_Footer',
    'IUIAutomationCacheRequest', 'IUIAutomationElementArray',
    'ProviderOptions_UseComThreading',
    'UIA_AnnotationTargetPropertyId',
    'UIA_StylesExtendedPropertiesPropertyId', 'DockPosition_Right',
    'UIA_MarginTrailingAttributeId', 'UIA_OutlineThicknessPropertyId',
    'UIA_MultipleViewPatternId', 'UIA_SayAsInterpretAsAttributeId',
    'UIA_DragIsGrabbedPropertyId', 'Off', 'UIA_PaneControlTypeId',
    'UIA_DragDropEffectsPropertyId', 'TreeScope_Element',
    'IUIAutomationElement2', 'UIA_SpreadsheetPatternId',
    'UIA_ControlTypePropertyId', 'AutomationElementMode_None',
    'UIA_IsTransformPattern2AvailablePropertyId',
    'ScrollAmount_NoAmount', 'UIA_IsSubscriptAttributeId',
    'CoalesceEventsOptions_Disabled',
    'UIA_ProviderDescriptionPropertyId', 'UIA_FillTypePropertyId',
    'UIA_IsDockPatternAvailablePropertyId',
    'IUIAutomationSpreadsheetItemPattern', 'IUIAutomationTextRange3',
    'TextUnit_Page', 'UIA_StrikethroughStyleAttributeId',
    'IUIAutomationGridPattern', 'UIA_GridColumnCountPropertyId',
    'UIA_AnnotationDateTimePropertyId',
    'IUIAutomationSelectionPattern2', 'UIA_RadioButtonControlTypeId',
    'StyleId_Heading1', 'UIA_LegacyIAccessiblePatternId',
    'WindowInteractionState_ReadyForUserInteraction',
    'UIA_AnimationStyleAttributeId', 'UIA_LayoutInvalidatedEventId',
    'IUIAutomationTreeWalker', 'NotificationKind',
    'UIA_GridRowCountPropertyId', 'UIA_ClickablePointPropertyId',
    'UIA_TransformPattern2Id',
    'UIA_ExpandCollapseExpandCollapseStatePropertyId',
    'WindowInteractionState_Running', 'UIA_MenuItemControlTypeId',
    'UIA_AcceleratorKeyPropertyId', 'TreeTraversalOptions',
    'UIA_FullDescriptionPropertyId', 'SupportedTextSelection',
    'UIA_CustomLandmarkTypeId', 'UIA_SystemAlertEventId',
    'StyleId_Heading7', 'UIA_FillColorPropertyId',
    'UIA_WindowIsModalPropertyId',
    'IUIAutomationLegacyIAccessiblePattern',
    'IUIAutomationTextEditTextChangedEventHandler',
    'UIA_RangeValueLargeChangePropertyId',
    'UIA_IsSynchronizedInputPatternAvailablePropertyId',
    'UIA_LabeledByPropertyId', 'AnnotationType_Header',
    'TextUnit_Document', 'UIA_HorizontalTextAlignmentAttributeId',
    'UIA_IsVirtualizedItemPatternAvailablePropertyId',
    'UIA_IsActiveAttributeId', 'UIA_DragGrabbedItemsPropertyId',
    'IUIAutomationScrollItemPattern', 'IAccessible',
    'UIA_IsItalicAttributeId', 'TextEditChangeType_Composition',
    'TextPatternRangeEndpoint_End', 'Assertive',
    'UIA_VisualEffectsPropertyId',
    'TreeTraversalOptions_LastToFirstOrder', 'CoalesceEventsOptions',
    'UIA_ToolTipControlTypeId', 'UIA_AccessKeyPropertyId',
    'UIA_IsTransformPatternAvailablePropertyId',
    'UIA_IsValuePatternAvailablePropertyId', 'UIA_FormLandmarkTypeId',
    'IUIAutomation5', 'UIA_LandmarkTypePropertyId',
    'UIA_SplitButtonControlTypeId',
    'UIA_WindowWindowVisualStatePropertyId',
    'UIA_IsContentElementPropertyId', 'IUIAutomationElement6',
    'UIA_WindowPatternId',
    'UIA_IsMultipleViewPatternAvailablePropertyId',
    'UIA_StrikethroughColorAttributeId',
    'UIA_WindowWindowInteractionStatePropertyId',
    'IUIAutomationPropertyCondition', 'UIA_ObjectModelPatternId',
    'UIA_IsCustomNavigationPatternAvailablePropertyId',
    'IUIAutomationTableItemPattern', 'UIA_ExpandCollapsePatternId',
    'AnnotationType_Comment', 'DockPosition_Top',
    'IUIAutomationWindowPattern', 'ZoomUnit_LargeDecrement',
    'UIA_HelpTextPropertyId', 'OrientationType_Vertical',
    'HeadingLevel2', 'UIA_LinkAttributeId', 'UIA_IsDialogPropertyId',
    'UIA_Text_TextChangedEventId', 'IUIAutomationNotCondition',
    'UIA_DropTarget_DragLeaveEventId',
    'IUIAutomationPropertyChangedEventHandler', 'StyleId_Custom',
    'IUIAutomationScrollPattern',
    'StructureChangeType_ChildrenReordered',
    'UIA_UnderlineColorAttributeId',
    'UIA_RangeValueIsReadOnlyPropertyId',
    'UIA_LegacyIAccessibleDescriptionPropertyId',
    'PropertyConditionFlags_IgnoreCase',
    'IUIAutomationCustomNavigationPattern',
    'TextPatternRangeEndpoint_Start', 'UIA_OverlineStyleAttributeId',
    'TreeScope_None', 'UIA_IsTextChildPatternAvailablePropertyId',
    'WindowInteractionState_BlockedByModalWindow',
    'ProviderOptions_OverrideProvider',
    'UIA_TableItemColumnHeaderItemsPropertyId', 'UIA_DockPatternId',
    'AutomationElementMode_Full', 'NotificationProcessing_MostRecent',
    'UIA_SynchronizedInputPatternId', 'PropertyConditionFlags_None',
    'UIA_AutomationIdPropertyId',
    'UIA_IsTablePatternAvailablePropertyId',
    'UIA_ToolTipClosedEventId', 'NotificationKind_ActionAborted',
    'UIA_LiveSettingPropertyId', 'IUIAutomation6',
    'UIA_IndentationLeadingAttributeId', 'UIA_TextControlTypeId',
    'TextEditChangeType_AutoCorrect',
    'UIA_RangeValueMaximumPropertyId', 'UIA_InvokePatternId',
    'UIA_IsExpandCollapsePatternAvailablePropertyId',
    'SynchronizedInputType_RightMouseUp',
    'UIA_AnnotationAnnotationTypeIdPropertyId', 'UiaChangeInfo',
    'UIA_AnnotationTypesPropertyId', 'ToggleState_Off',
    'HeadingLevel_None', 'UIA_ScrollVerticallyScrollablePropertyId',
    'IUIAutomationSelectionPattern', 'UIA_SearchLandmarkTypeId',
    'AutomationElementMode', 'CUIAutomation',
    'UIA_LegacyIAccessibleDefaultActionPropertyId',
    'UIA_IsDataValidForFormPropertyId',
    'UIA_AutomationPropertyChangedEventId',
    'ScrollAmount_LargeIncrement', 'UIA_HeaderControlTypeId',
    'StructureChangeType',
    'UIA_LegacyIAccessibleKeyboardShortcutPropertyId',
    'UIA_HostedFragmentRootsInvalidatedEventId',
    'UIA_CultureAttributeId', 'UIA_InputReachedTargetEventId',
    'UIA_IndentationTrailingAttributeId', 'UIA_ItemStatusPropertyId',
    'HeadingLevel5', 'TextUnit_Paragraph',
    'WindowVisualState_Maximized', 'AnnotationType_Mathematics',
    'ScrollAmount_SmallIncrement', 'UIA_StylesFillColorPropertyId',
    'NavigateDirection_LastChild', 'IUIAutomationTextRange',
    'UIA_NamePropertyId', 'StructureChangeType_ChildAdded',
    'IUIAutomationNotificationEventHandler', 'UIA_RotationPropertyId',
    'IUIAutomationProxyFactoryEntry',
    'UIA_Transform2CanZoomPropertyId',
    'UIA_IndentationFirstLineAttributeId',
    'UIA_IsReadOnlyAttributeId', 'ScrollAmount',
    'TextPatternRangeEndpoint', 'StyleId_Heading4', 'ZoomUnit',
    'UIA_DropTarget_DroppedEventId', 'UIA_EditControlTypeId',
    'IUIAutomationProxyFactory', 'UIA_CustomNavigationPatternId',
    'IUIAutomationTogglePattern',
    'UIA_InputReachedOtherElementEventId', 'OrientationType_None',
    'UIA_RangeValueMinimumPropertyId',
    'UIA_BoundingRectanglePropertyId',
    'UIA_BeforeParagraphSpacingAttributeId', 'UIA_TreeControlTypeId',
    'WindowVisualState_Minimized', 'IUIAutomationOrCondition',
    'UIA_BackgroundColorAttributeId',
    'UIA_ActiveTextPositionChangedEventId',
    'UIA_FrameworkIdPropertyId', 'SynchronizedInputType_KeyDown',
    'TextEditChangeType_None', 'StyleId_Heading6',
    'UIA_IsSelectionItemPatternAvailablePropertyId',
    'UIA_MainLandmarkTypeId', 'UIA_MenuModeEndEventId',
    'ExtendedProperty', 'UIA_IsTextPatternAvailablePropertyId',
    'UIA_UnderlineStyleAttributeId',
    'ProviderOptions_RefuseNonClientSupport',
    'UIA_Window_WindowClosedEventId', 'UIA_PositionInSetPropertyId',
    'UIA_AriaRolePropertyId', 'UIA_ForegroundColorAttributeId',
    'UIA_ScrollHorizontallyScrollablePropertyId',
    'UIA_Transform2ZoomLevelPropertyId', 'ExpandCollapseState',
    'IUIAutomation4', 'WindowInteractionState_Closing',
    'UIA_SliderControlTypeId', 'UIA_LegacyIAccessibleStatePropertyId',
    'UIA_MenuBarControlTypeId', 'IUIAutomationDockPattern',
    'StyleId_NumberedList', 'UIA_IsSuperscriptAttributeId',
    'AnnotationType_DeletionChange',
    'UIA_IsStylesPatternAvailablePropertyId',
    'UIA_LegacyIAccessibleSelectionPropertyId',
    'UIA_StatusBarControlTypeId', 'AnnotationType_Footnote', 'Polite',
    'IUIAutomationCondition', 'StyleId_Heading9',
    'UIA_SelectionItemIsSelectedPropertyId',
    'NotificationProcessing_ImportantMostRecent',
    'UIA_ChangesEventId', 'UIA_Drag_DragCompleteEventId',
    'TextEditChangeType_AutoComplete',
    'UIA_IsKeyboardFocusablePropertyId',
    'UIA_LegacyIAccessibleNamePropertyId',
    'UIA_TitleBarControlTypeId', 'NotificationProcessing_All',
    'UIA_TogglePatternId', 'UIA_TransformCanMovePropertyId',
    'IUIAutomation3', 'IUIAutomationElement9',
    'AnnotationType_MoveChange', 'StyleId_Heading3',
    'UIA_SpreadsheetItemAnnotationTypesPropertyId',
    'UIA_WindowCanMaximizePropertyId', 'UIA_TablePatternId',
    'RowOrColumnMajor_ColumnMajor', 'ExpandCollapseState_LeafNode',
    'UIA_IsRequiredForFormPropertyId',
    'IUIAutomationTransformPattern', 'UIA_TabItemControlTypeId',
    'AnnotationType_Highlighted', 'TreeScope_Descendants',
    'ProviderOptions_ServerSideProvider', 'UIA_TableControlTypeId',
    'UIA_ScrollHorizontalScrollPercentPropertyId',
    'UIA_ScrollBarControlTypeId',
    'ExpandCollapseState_PartiallyExpanded',
    'ZoomUnit_LargeIncrement', 'OrientationType',
    'UIA_IsDropTargetPatternAvailablePropertyId',
    'IUIAutomationTransformPattern2',
    'SynchronizedInputType_LeftMouseDown',
    'UIA_VirtualizedItemPatternId', 'UIA_TextPattern2Id',
    'StyleId_Title', 'UIA_StylesStyleNamePropertyId',
    'UIA_Transform2ZoomMinimumPropertyId', 'LiveSetting',
    'ConnectionRecoveryBehaviorOptions_Disabled',
    'UIA_TableItemPatternId',
    'UIA_ScrollHorizontalViewSizePropertyId',
    'AnnotationType_Sensitive', 'UIA_StylesPatternId',
    'UIA_TabControlTypeId', 'UIA_SelectionPatternId',
    'IUIAutomationBoolCondition', 'IUIAutomationElement3',
    'UIA_MarginLeadingAttributeId', 'DockPosition_Bottom',
    'UIA_Window_WindowOpenedEventId', 'AnnotationType_Endnote',
    'UIA_FontSizeAttributeId', 'ScrollAmount_LargeDecrement',
    'UIA_TreeItemControlTypeId',
    'UIA_SelectionCanSelectMultiplePropertyId',
    'UIA_AnnotationAnnotationTypeNamePropertyId',
    'UIA_AnnotationAuthorPropertyId', 'IUIAutomationElement5',
    'AnnotationType_GrammarError', 'StyleId_Subtitle',
    'SupportedTextSelection_Multiple', 'UIA_OutlineStylesAttributeId',
    'UIA_Selection_InvalidatedEventId',
    'UIA_IsItemContainerPatternAvailablePropertyId',
    'UIA_IsSelectionPatternAvailablePropertyId',
    'UIA_MarginBottomAttributeId', 'StyleId_Normal',
    'UIA_LiveRegionChangedEventId',
    'UIA_IsScrollPatternAvailablePropertyId', 'IUIAutomation',
    'StyleId_Emphasis', 'UIA_TransformPatternId',
    'UIA_TableColumnHeadersPropertyId',
    'ProviderOptions_HasNativeIAccessible',
    'IUIAutomationActiveTextPositionChangedEventHandler',
    'UIA_GridPatternId', 'UIA_DropTargetPatternId',
    'UIA_IsTextPattern2AvailablePropertyId',
    'IUIAutomationStylesPattern',
    'AnnotationType_CircularReferenceError',
    'NavigateDirection_PreviousSibling', 'UIA_CulturePropertyId',
    'UIA_IsGridPatternAvailablePropertyId',
    'RowOrColumnMajor_RowMajor', 'UIA_AnnotationObjectsAttributeId',
    'IUIAutomation2', 'IUIAutomationSelectionItemPattern',
    'UIA_NativeWindowHandlePropertyId', 'typelib_path',
    'UIA_OrientationPropertyId', 'UIA_ControllerForPropertyId',
    'UIA_IsSpreadsheetItemPatternAvailablePropertyId',
    'UIA_StylesFillPatternColorPropertyId',
    'UIA_IsPeripheralPropertyId',
    'NotificationProcessing_CurrentThenMostRecent',
    'UIA_SpreadsheetItemAnnotationObjectsPropertyId',
    'UIA_IsTextEditPatternAvailablePropertyId',
    'UIA_AfterParagraphSpacingAttributeId',
    'IUIAutomationFocusChangedEventHandler',
    'IUIAutomationTextPattern', 'UIA_SeparatorControlTypeId',
    'UIA_ScrollVerticalViewSizePropertyId', 'DockPosition',
    'UIA_SelectionPattern2Id', 'IUIAutomationVirtualizedItemPattern',
    'SynchronizedInputType_RightMouseDown'
]

