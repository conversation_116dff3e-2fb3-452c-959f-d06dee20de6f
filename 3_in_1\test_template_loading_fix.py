#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板加载修复效果
作者: Cursor AI
创建时间: 2025-07-27
"""

import sys
import os
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
project_root = current_dir
sys.path.insert(0, str(project_root / "opencv_yolo" / "src"))

def test_template_loading():
    """测试模板加载功能"""
    print("🧪 测试模板加载修复效果")
    print("=" * 50)
    
    # 导入修复后的函数
    try:
        from yolo_opencv_detector.gui.dialogs.source_code_dialog import load_template_image_by_name
        print("✅ 成功导入修复后的模板加载函数")
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        return False
    
    # 测试用例
    test_cases = [
        "[通用] 本地磁盘",  # GUI显示的格式
        "本地磁盘",        # 清理后的格式
        "[通用] E",        # 另一个模板
        "E",              # 清理后的格式
    ]
    
    print("\n🔍 开始测试模板加载...")
    success_count = 0
    
    for template_name in test_cases:
        print(f"\n📋 测试模板: {template_name}")
        print("-" * 30)
        
        try:
            image = load_template_image_by_name(template_name)
            if image is not None:
                print(f"✅ 成功加载模板: {template_name}")
                print(f"   图像尺寸: {image.shape}")
                success_count += 1
            else:
                print(f"❌ 未能加载模板: {template_name}")
        except Exception as e:
            print(f"❌ 加载过程出错: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个模板加载成功")
    
    if success_count > 0:
        print("🎉 修复成功！至少有一个模板能够正确加载")
        return True
    else:
        print("⚠️ 仍有问题，需要进一步调试")
        return False

def list_available_templates():
    """列出可用的模板文件"""
    print("\n📁 列出可用的模板文件:")
    print("-" * 30)
    
    template_dirs = [
        Path("templates"),
        Path("opencv_yolo/templates"),
        Path("3_in_1/opencv_yolo/templates"),
    ]
    
    extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
    
    for template_dir in template_dirs:
        if template_dir.exists():
            print(f"\n📂 目录: {template_dir}")
            files = []
            for file_path in template_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in extensions:
                    files.append(file_path.name)
            
            if files:
                for file_name in sorted(files):
                    print(f"   📄 {file_name}")
            else:
                print("   (空目录)")
        else:
            print(f"\n📂 目录: {template_dir} (不存在)")

if __name__ == "__main__":
    print("🚀 开始测试模板加载修复...")
    
    # 设置工作目录
    os.chdir(current_dir)
    print(f"📍 当前工作目录: {os.getcwd()}")
    
    # 列出可用模板
    list_available_templates()
    
    # 测试模板加载
    success = test_template_loading()
    
    if success:
        print("\n🎉 测试完成！模板加载功能已修复")
    else:
        print("\n⚠️ 测试完成，但仍需进一步调试")
