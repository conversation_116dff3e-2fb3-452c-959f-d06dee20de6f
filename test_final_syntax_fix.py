#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终语法修复
"""

import sys
from pathlib import Path
from datetime import datetime

def test_import_and_basic_functionality():
    """测试导入和基本功能"""
    try:
        print("🧪 测试最终语法修复...")
        
        # 添加项目路径
        project_root = Path(__file__).parent / "3_in_1" / "opencv_yolo" / "src"
        sys.path.insert(0, str(project_root))
        
        print("✅ 路径设置成功")
        
        # 测试导入
        from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog
        print("✅ 源代码对话框导入成功")
        
        # 创建实例（不显示GUI）
        dialog = SourceCodeDialog()
        print("✅ 对话框实例创建成功")
        
        # 测试GUI状态捕获方法
        gui_state = {
            'confidence_threshold': 0.75,
            'nms_threshold': 0.45,
            'selected_template': '测试模板',
            'template_path': None,
            'detection_interval': 2.0,
            'export_timestamp': datetime.now().isoformat(),
            'auto_detect': True,
            'capture_method': 'test'
        }
        
        print("✅ 模拟GUI状态创建成功")
        
        # 测试静态快照代码生成
        static_code = dialog._generate_static_snapshot_code(gui_state)
        print("✅ 静态快照代码生成成功")
        print(f"📝 代码长度: {len(static_code)} 字符")
        
        # 测试简单演示代码生成
        demo_code = dialog._generate_simple_detection_demo(gui_state)
        print("✅ 简单演示代码生成成功")
        print(f"📝 演示代码长度: {len(demo_code)} 字符")
        
        # 保存生成的代码进行验证
        with open("test_generated_static.py", 'w', encoding='utf-8') as f:
            f.write(static_code)
        
        with open("test_generated_demo.py", 'w', encoding='utf-8') as f:
            f.write(demo_code)
        
        print("💾 生成的代码已保存到文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_generated_code_execution():
    """测试生成的代码是否可执行"""
    try:
        print("\n🚀 测试生成的代码执行...")
        
        # 测试静态快照代码
        print("📄 测试静态快照代码...")
        with open("test_generated_static.py", 'r', encoding='utf-8') as f:
            static_code = f.read()
        
        exec(static_code)
        print("✅ 静态快照代码执行成功")
        
        # 测试演示代码
        print("\n📄 测试演示代码...")
        with open("test_generated_demo.py", 'r', encoding='utf-8') as f:
            demo_code = f.read()
        
        exec(demo_code)
        print("✅ 演示代码执行成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 代码执行测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 开始最终语法修复测试...")
    
    # 测试1：导入和基本功能
    success1 = test_import_and_basic_functionality()
    
    # 测试2：生成的代码执行
    success2 = test_generated_code_execution() if success1 else False
    
    if success1 and success2:
        print("\n🎉 所有测试通过！语法修复成功！")
        print("💡 现在用户可以正常使用源代码对话框功能了！")
        print("✅ 主要修复内容：")
        print("   • 修复了未闭合的三引号字符串")
        print("   • 移除了不应该存在的代码片段")
        print("   • 简化了静态快照代码生成")
        print("   • 避免了f-string中的花括号冲突")
    else:
        print("\n💥 测试失败！需要进一步检查！")
