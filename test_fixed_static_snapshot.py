#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（简化版本）
导出时间: 2025-07-27T12:00:34.937403

此脚本是导出时刻GUI状态的完全静态快照，包含以下固化参数：
- 置信度阈值: 0.75
- NMS阈值: 0.45
- 选择模板: 测试模板
- 检测间隔: 2.0秒
- 自动检测: True

注意：此脚本不会动态读取GUI配置，参数已固化。
如需不同参数，请重新生成新的状态快照。
"""

import sys
import os
from pathlib import Path

# 基础导入
try:
    import numpy as np
    import cv2
    from datetime import datetime
    print("✅ 基础模块导入成功")
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保安装了必要的依赖包：pip install numpy opencv-python")
    sys.exit(1)

# ============================================================================
# 导出时刻的固化参数（静态快照）
# ============================================================================
EXPORTED_CONFIDENCE_THRESHOLD = 0.750
EXPORTED_NMS_THRESHOLD = 0.450
EXPORTED_TEMPLATE_NAME = '测试模板'
EXPORTED_DETECTION_INTERVAL = 2.0
EXPORTED_AUTO_DETECT = True
EXPORTED_TIMESTAMP = "2025-07-27T12:00:34.937403"

def main():
    """主函数 - 简化版本"""
    print("🎯 YOLO检测器 - 静态快照版本（简化版）")
    print(f"📅 导出时间: {EXPORTED_TIMESTAMP}")
    print("⚙️  固化参数:")
    print(f"   • 置信度阈值: {EXPORTED_CONFIDENCE_THRESHOLD}")
    print(f"   • NMS阈值: {EXPORTED_NMS_THRESHOLD}")
    print(f"   • 选择模板: {EXPORTED_TEMPLATE_NAME}")
    print(f"   • 检测间隔: {EXPORTED_DETECTION_INTERVAL}秒")
    print(f"   • 自动检测: {EXPORTED_AUTO_DETECT}")
    
    print("\n✅ 静态快照代码运行成功！")
    print("💡 这是一个简化版本，展示了导出时刻的GUI状态参数。")
    print("🔧 如需完整的检测功能，请使用导出的独立脚本。")

if __name__ == "__main__":
    main()
