#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板管理器

统一的模板管理核心，提供所有模板相关操作的单一入口点。

Created: 2025-07-27
Author: Augment Agent
"""

import time
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union, Any
import threading
import numpy as np

from .template_models import (
    Template, TemplateCategory, TemplateStatus, TemplateMetadata, 
    TemplateImageInfo, TemplateSearchCriteria, TemplateOperationResult
)
from .template_repository import TemplateRepository
from .template_cache import TemplateCache
from .template_validator import TemplateValidator, clean_template_name, build_display_name
from .template_exceptions import (
    TemplateError, TemplateNotFoundError, TemplateValidationError,
    TemplateDuplicateError, handle_template_errors
)


class TemplateManager:
    """模板管理器 - 单例模式"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            with cls._lock:
                if not cls._instance:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self, storage_dir: Union[str, Path] = "templates", config_format: str = "json"):
        # 避免重复初始化
        if hasattr(self, '_initialized'):
            return
        
        self.storage_dir = Path(storage_dir)
        self.repository = TemplateRepository(storage_dir, config_format)
        self.cache = TemplateCache()
        self.validator = TemplateValidator()
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 标记已初始化
        self._initialized = True
        
        # 预加载模板
        self._preload_templates()
    
    @handle_template_errors
    def create_template(
        self,
        name: str,
        image_path: Union[str, Path],
        category: Union[str, TemplateCategory] = TemplateCategory.GENERAL,
        description: str = "",
        threshold: float = 0.8,
        method: str = "TM_CCOEFF_NORMED",
        tags: List[str] = None,
        **kwargs
    ) -> TemplateOperationResult:
        """
        创建新模板
        
        Args:
            name: 模板名称
            image_path: 图像文件路径
            category: 模板分类
            description: 描述
            threshold: 匹配阈值
            method: 匹配方法
            tags: 标签列表
            **kwargs: 其他参数
            
        Returns:
            操作结果
        """
        with self.lock:
            try:
                # 清理模板名称
                clean_name = clean_template_name(name)
                
                # 处理分类
                if isinstance(category, str):
                    category = TemplateCategory(category)
                
                # 生成模板ID
                template_id = kwargs.get('template_id', str(uuid.uuid4()))
                
                # 检查重复
                if self._template_exists(template_id):
                    return TemplateOperationResult(
                        success=False,
                        error_message=f"模板ID已存在: {template_id}",
                        error_code="DUPLICATE_ID"
                    )
                
                # 验证图像文件
                image_path = Path(image_path)
                image_errors = self.validator.validate_image_file(image_path)
                if image_errors:
                    return TemplateOperationResult(
                        success=False,
                        error_message=f"图像文件验证失败: {'; '.join(image_errors)}",
                        error_code="INVALID_IMAGE"
                    )
                
                # 加载图像
                image = self._load_image(image_path)
                if image is None:
                    return TemplateOperationResult(
                        success=False,
                        error_message="无法加载图像文件",
                        error_code="IMAGE_LOAD_FAILED"
                    )
                
                # 复制图像到存储目录
                stored_image_path = self.repository.copy_image_to_storage(image_path, template_id)
                
                # 创建图像信息
                image_info = TemplateImageInfo.from_image(image, stored_image_path)
                
                # 创建元数据
                metadata = TemplateMetadata(
                    tags=tags or [],
                    custom_fields=kwargs
                )
                
                # 创建模板对象
                template = Template(
                    id=template_id,
                    name=clean_name,
                    display_name=build_display_name(clean_name, category),
                    description=description,
                    category=category,
                    status=TemplateStatus.ACTIVE,
                    file_path=stored_image_path,
                    relative_path=str(stored_image_path.relative_to(self.storage_dir)),
                    image_info=image_info,
                    threshold=threshold,
                    method=method,
                    metadata=metadata
                )
                
                # 验证模板
                self.validator.raise_if_invalid(template)
                
                # 保存到存储
                self.repository.save_template(template)
                
                # 缓存模板和图像
                self.cache.cache_template(template, image)
                
                return TemplateOperationResult(
                    success=True,
                    template=template,
                    details={'template_id': template_id}
                )
                
            except Exception as e:
                return TemplateOperationResult(
                    success=False,
                    error_message=str(e),
                    error_code=getattr(e, 'error_code', 'UNKNOWN_ERROR')
                )
    
    @handle_template_errors
    def get_template(self, template_id: str) -> Optional[Template]:
        """
        获取模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板对象或None
        """
        with self.lock:
            # 先从缓存获取
            template = self.cache.metadata_cache.get_template(template_id)
            if template:
                return template
            
            # 从存储加载
            try:
                template = self.repository.load_template(template_id)
                self.cache.metadata_cache.put_template(template)
                return template
            except TemplateNotFoundError:
                return None
    
    @handle_template_errors
    def get_template_with_image(self, template_id: str) -> Optional[Tuple[Template, np.ndarray]]:
        """
        获取模板及其图像
        
        Args:
            template_id: 模板ID
            
        Returns:
            (模板对象, 图像数组) 或 None
        """
        with self.lock:
            # 先从缓存获取
            cached_result = self.cache.get_template_with_image(template_id)
            if cached_result:
                return cached_result
            
            # 获取模板
            template = self.get_template(template_id)
            if not template:
                return None
            
            # 加载图像
            image = self._load_image(template.file_path)
            if image is None:
                return None
            
            # 缓存结果
            self.cache.cache_template(template, image)
            
            return template, image
    
    @handle_template_errors
    def update_template(self, template_id: str, **updates) -> TemplateOperationResult:
        """
        更新模板
        
        Args:
            template_id: 模板ID
            **updates: 更新字段
            
        Returns:
            操作结果
        """
        with self.lock:
            try:
                # 获取现有模板
                template = self.get_template(template_id)
                if not template:
                    return TemplateOperationResult(
                        success=False,
                        error_message=f"模板不存在: {template_id}",
                        error_code="TEMPLATE_NOT_FOUND"
                    )
                
                # 处理名称清理
                if 'name' in updates:
                    updates['name'] = clean_template_name(updates['name'])
                
                # 处理分类
                if 'category' in updates and isinstance(updates['category'], str):
                    updates['category'] = TemplateCategory(updates['category'])
                
                # 创建更新后的模板
                updated_template = template.update(**updates)
                
                # 验证更新后的模板
                self.validator.raise_if_invalid(updated_template)
                
                # 保存到存储
                self.repository.save_template(updated_template)
                
                # 更新缓存
                self.cache.metadata_cache.put_template(updated_template)
                
                return TemplateOperationResult(
                    success=True,
                    template=updated_template
                )
                
            except Exception as e:
                return TemplateOperationResult(
                    success=False,
                    error_message=str(e),
                    error_code=getattr(e, 'error_code', 'UNKNOWN_ERROR')
                )
    
    @handle_template_errors
    def delete_template(self, template_id: str) -> TemplateOperationResult:
        """
        删除模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            操作结果
        """
        with self.lock:
            try:
                # 检查模板是否存在
                template = self.get_template(template_id)
                if not template:
                    return TemplateOperationResult(
                        success=False,
                        error_message=f"模板不存在: {template_id}",
                        error_code="TEMPLATE_NOT_FOUND"
                    )
                
                # 从存储删除
                success = self.repository.delete_template(template_id)
                
                if success:
                    # 从缓存移除
                    self.cache.remove_template(template_id)
                    
                    return TemplateOperationResult(
                        success=True,
                        details={'deleted_template_id': template_id}
                    )
                else:
                    return TemplateOperationResult(
                        success=False,
                        error_message="删除操作失败",
                        error_code="DELETE_FAILED"
                    )
                    
            except Exception as e:
                return TemplateOperationResult(
                    success=False,
                    error_message=str(e),
                    error_code=getattr(e, 'error_code', 'UNKNOWN_ERROR')
                )
    
    @handle_template_errors
    def search_templates(self, criteria: TemplateSearchCriteria) -> List[Template]:
        """
        搜索模板
        
        Args:
            criteria: 搜索条件
            
        Returns:
            匹配的模板列表
        """
        return self.repository.search_templates(criteria)
    
    @handle_template_errors
    def get_all_templates(self) -> Dict[str, Template]:
        """
        获取所有模板
        
        Returns:
            模板字典
        """
        return self.repository.load_all_templates()
    
    def get_template_by_name(self, name: str, category: Optional[TemplateCategory] = None) -> Optional[Template]:
        """
        根据名称获取模板
        
        Args:
            name: 模板名称（支持显示名称和清理后名称）
            category: 可选的分类过滤
            
        Returns:
            模板对象或None
        """
        clean_name = clean_template_name(name)
        
        templates = self.get_all_templates()
        for template in templates.values():
            # 匹配清理后的名称或显示名称
            if (template.clean_name == clean_name or 
                template.name == name or 
                template.display_name == name):
                
                if category is None or template.category == category:
                    return template
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """获取管理器统计信息"""
        storage_stats = self.repository.get_storage_stats()
        cache_stats = self.cache.get_stats()
        
        return {
            'storage': storage_stats,
            'cache': cache_stats,
            'manager_info': {
                'storage_dir': str(self.storage_dir),
                'initialized': hasattr(self, '_initialized')
            }
        }
    
    def _preload_templates(self) -> None:
        """预加载模板到缓存"""
        try:
            templates = self.repository.load_all_templates()
            for template in templates.values():
                self.cache.metadata_cache.put_template(template)
        except Exception as e:
            print(f"⚠️ 预加载模板失败: {e}")
    
    def _template_exists(self, template_id: str) -> bool:
        """检查模板是否存在"""
        return self.get_template(template_id) is not None
    
    def _load_image(self, image_path: Path) -> Optional[np.ndarray]:
        """加载图像文件"""
        try:
            import cv2
            
            if not image_path.exists():
                return None
            
            # 使用支持中文路径的方法
            image_data = np.fromfile(str(image_path), dtype=np.uint8)
            image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)
            
            return image
        except Exception:
            return None


# 全局模板管理器实例
_global_template_manager: Optional[TemplateManager] = None


def get_template_manager(storage_dir: Union[str, Path] = "templates") -> TemplateManager:
    """
    获取全局模板管理器实例
    
    Args:
        storage_dir: 存储目录
        
    Returns:
        模板管理器实例
    """
    global _global_template_manager
    if _global_template_manager is None:
        _global_template_manager = TemplateManager(storage_dir)
    return _global_template_manager
