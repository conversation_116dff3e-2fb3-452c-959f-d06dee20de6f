from enum import IntFlag

import comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 as __wrapper_module__
from comtypes.gen._00020430_0000_0000_C000_000000000046_0_2_0 import (
    OLE_YSIZE_HIMETRIC, DIS<PERSON><PERSON>PERTY, _check_version, Gray, dispid,
    <PERSON><PERSON><PERSON><PERSON>s, Checked, Default, <PERSON><PERSON><PERSON>, <PERSON>ONTNAME, IEnumVARIANT,
    OLE_COLOR, _lcid, VgaColor, Library, Font, OLE_YPOS_HIMETRIC,
    HRESULT, COMMETHOD, IFontDisp, IDispatch, OLE_XSIZE_HIMETRIC,
    IFont, F<PERSON><PERSON><PERSON>L<PERSON>, Color, O<PERSON>_XPOS_CONTAINER, DISPMETHOD,
    OLE_ENABLEDEFAULTBOOL, DISPPARAMS, OLE_YSIZE_CONTAINER,
    OLE_YSIZE_PIXELS, OLE_YPOS_PIXELS, IFontEventsDisp, IPicture,
    EXCEPINFO, OLE_XSIZE_CONTAINER, IPictureDisp, BSTR, Picture,
    CoClass, OLE_HANDLE, OLE_YPOS_CONTAINER, OLE_OPTEXCLUSIVE,
    OLE_XPOS_HIMETRIC, OLE_CANCELBOOL, FONTBOLD, FONTSIZE, Unchecked,
    typelib_path, FONTSTRIKETHROUGH, OLE_XSIZE_PIXELS, FONTUNDERSCORE,
    StdFont, StdPicture, OLE_XPOS_PIXELS, IUnknown, Monochrome,
    VARIANT_BOOL
)


class OLE_TRISTATE(IntFlag):
    Unchecked = 0
    Checked = 1
    Gray = 2


class LoadPictureConstants(IntFlag):
    Default = 0
    Monochrome = 1
    VgaColor = 2
    Color = 4


__all__ = [
    'OLE_YSIZE_HIMETRIC', 'Gray', 'OLE_YSIZE_CONTAINER', 'FontEvents',
    'OLE_YPOS_PIXELS', 'OLE_YSIZE_PIXELS', 'IFontEventsDisp',
    'IPicture', 'Checked', 'Default', 'OLE_XSIZE_CONTAINER',
    'IPictureDisp', 'LoadPictureConstants', 'Picture', 'FONTNAME',
    'OLE_COLOR', 'OLE_HANDLE', 'OLE_YPOS_CONTAINER', 'VgaColor',
    'Library', 'OLE_XPOS_HIMETRIC', 'OLE_OPTEXCLUSIVE',
    'OLE_CANCELBOOL', 'FONTBOLD', 'Font', 'FONTSIZE',
    'OLE_YPOS_HIMETRIC', 'Unchecked', 'typelib_path', 'OLE_TRISTATE',
    'IFontDisp', 'OLE_XSIZE_PIXELS', 'FONTUNDERSCORE',
    'FONTSTRIKETHROUGH', 'StdFont', 'StdPicture', 'OLE_XPOS_PIXELS',
    'Monochrome', 'OLE_XSIZE_HIMETRIC', 'IFont', 'FONTITALIC',
    'Color', 'OLE_XPOS_CONTAINER', 'OLE_ENABLEDEFAULTBOOL'
]

