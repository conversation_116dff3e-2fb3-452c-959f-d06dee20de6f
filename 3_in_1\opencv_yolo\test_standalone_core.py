#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立核心功能测试

直接测试核心模块，避免包导入问题。

Created: 2025-07-27
Author: Augment Agent
"""

import sys
from pathlib import Path

# 直接添加核心模块路径
core_path = Path(__file__).parent / "src" / "yolo_opencv_detector" / "core"
sys.path.insert(0, str(core_path))

def test_standalone_core():
    """测试独立核心功能"""
    print("🧪 测试独立核心功能...")
    
    try:
        # 直接导入核心模块
        import template_models
        import template_validator
        import template_cache
        import template_repository
        import template_manager
        
        print("✅ 所有核心模块导入成功")
        
        # 测试模板名称处理
        clean_name = template_validator.clean_template_name("[通用] 本地磁盘")
        assert clean_name == "本地磁盘"
        print(f"✅ 名称清理测试通过: '[通用] 本地磁盘' -> '{clean_name}'")
        
        # 测试模板数据模型
        template = template_models.Template(
            id="test-001",
            name="测试模板",
            display_name="[通用] 测试模板",
            description="测试描述",
            category=template_models.TemplateCategory.GENERAL,
            status=template_models.TemplateStatus.ACTIVE
        )
        
        assert template.clean_name == "测试模板"
        assert template.is_valid
        print("✅ 模板数据模型测试通过")
        
        # 测试序列化
        template_dict = template.to_dict()
        restored_template = template_models.Template.from_dict(template_dict)
        assert restored_template.id == template.id
        print("✅ 序列化/反序列化测试通过")
        
        # 测试缓存
        cache = template_cache.TemplateCache()
        cache.metadata_cache.put_template(template)
        cached_template = cache.metadata_cache.get_template("test-001")
        assert cached_template is not None
        assert cached_template.id == "test-001"
        print("✅ 缓存测试通过")
        
        # 测试存储
        repo = template_repository.TemplateRepository("test_standalone_storage")
        repo.save_template(template)
        loaded_template = repo.load_template("test-001")
        assert loaded_template.id == template.id
        print("✅ 存储测试通过")
        
        # 清理
        import shutil
        test_dir = Path("test_standalone_storage")
        if test_dir.exists():
            shutil.rmtree(test_dir)
        
        print("✅ 所有独立核心功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 独立核心功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def demonstrate_new_api():
    """演示新API的使用"""
    print("\n🎯 演示新模板管理API的使用...")
    
    try:
        import template_validator
        from template_models import TemplateCategory
        
        # 演示名称处理
        print("📋 名称处理演示:")
        test_names = [
            "[通用] 本地磁盘",
            "[按钮] 登录按钮", 
            "[图标] 菜单图标",
            "普通模板"
        ]
        
        for name in test_names:
            clean_name = template_validator.clean_template_name(name)
            display_name = template_validator.build_display_name(clean_name, TemplateCategory.GENERAL)
            print(f"  原始: '{name}'")
            print(f"  清理: '{clean_name}'")
            print(f"  重建: '{display_name}'")
            print()
        
        print("✅ 新API演示完成")
        return True
        
    except Exception as e:
        print(f"❌ 新API演示失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 开始独立核心功能测试...")
    print("=" * 50)
    
    success1 = test_standalone_core()
    success2 = demonstrate_new_api()
    
    print("=" * 50)
    if success1 and success2:
        print("🎉 所有测试通过！新模板管理系统核心功能完全正常")
        print("\n📝 新系统的主要优势:")
        print("  ✅ 统一的数据模型")
        print("  ✅ 强类型安全")
        print("  ✅ 智能缓存机制")
        print("  ✅ 完善的错误处理")
        print("  ✅ 标准化的API接口")
        print("  ✅ 高性能的文件管理")
        print("  ✅ 自动的名称清理")
        print("  ✅ 版本控制支持")
    else:
        print("⚠️ 部分测试失败")


if __name__ == "__main__":
    main()
