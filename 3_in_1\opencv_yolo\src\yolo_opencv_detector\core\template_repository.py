#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板存储层

负责模板数据的持久化存储，支持多种存储格式和版本控制。

Created: 2025-07-27
Author: Augment Agent
"""

import json
import yaml
import time
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
import threading

from .template_models import Template, TemplateCategory, TemplateStatus, TemplateSearchCriteria
from .template_exceptions import TemplateRepositoryError, TemplateNotFoundError, handle_template_errors


class TemplateRepository:
    """模板存储仓库"""
    
    # 支持的配置文件格式
    SUPPORTED_FORMATS = {'.json', '.yaml', '.yml'}
    
    def __init__(self, storage_dir: Union[str, Path], config_format: str = 'json'):
        self.storage_dir = Path(storage_dir)
        self.config_format = config_format.lower()
        self.config_file = self.storage_dir / f"templates.{self.config_format}"
        self.images_dir = self.storage_dir / "images"
        self.backup_dir = self.storage_dir / "backups"
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 验证格式
        if f".{self.config_format}" not in self.SUPPORTED_FORMATS:
            raise TemplateRepositoryError(
                str(self.storage_dir),
                "init",
                f"不支持的配置格式: {self.config_format}"
            )
        
        # 初始化目录结构
        self._init_directories()
    
    def _init_directories(self) -> None:
        """初始化目录结构"""
        try:
            self.storage_dir.mkdir(parents=True, exist_ok=True)
            self.images_dir.mkdir(exist_ok=True)
            self.backup_dir.mkdir(exist_ok=True)
        except Exception as e:
            raise TemplateRepositoryError(
                str(self.storage_dir),
                "init",
                f"创建目录失败: {e}"
            )
    
    @handle_template_errors
    def save_template(self, template: Template) -> None:
        """
        保存单个模板
        
        Args:
            template: 要保存的模板
        """
        with self.lock:
            # 加载现有模板
            templates = self.load_all_templates()
            
            # 更新或添加模板
            templates[template.id] = template
            
            # 保存所有模板
            self._save_templates_to_file(templates)
    
    @handle_template_errors
    def save_templates(self, templates: Dict[str, Template]) -> None:
        """
        批量保存模板
        
        Args:
            templates: 模板字典
        """
        with self.lock:
            self._save_templates_to_file(templates)
    
    @handle_template_errors
    def load_template(self, template_id: str) -> Template:
        """
        加载单个模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            模板对象
            
        Raises:
            TemplateNotFoundError: 模板不存在
        """
        templates = self.load_all_templates()
        if template_id not in templates:
            raise TemplateNotFoundError(template_id)
        return templates[template_id]
    
    @handle_template_errors
    def load_all_templates(self) -> Dict[str, Template]:
        """
        加载所有模板
        
        Returns:
            模板字典
        """
        with self.lock:
            if not self.config_file.exists():
                return {}
            
            try:
                # 读取配置文件
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    if self.config_format == 'json':
                        data = json.load(f)
                    else:  # yaml
                        data = yaml.safe_load(f)
                
                # 解析模板数据
                templates = {}
                template_list = data.get('templates', [])
                
                for template_data in template_list:
                    try:
                        template = Template.from_dict(template_data)
                        templates[template.id] = template
                    except Exception as e:
                        # 记录错误但继续加载其他模板
                        print(f"⚠️ 加载模板失败: {template_data.get('id', 'unknown')} - {e}")
                
                return templates
                
            except Exception as e:
                raise TemplateRepositoryError(
                    str(self.config_file),
                    "load",
                    f"加载配置文件失败: {e}"
                )
    
    @handle_template_errors
    def delete_template(self, template_id: str) -> bool:
        """
        删除模板
        
        Args:
            template_id: 模板ID
            
        Returns:
            是否删除成功
        """
        with self.lock:
            templates = self.load_all_templates()
            
            if template_id not in templates:
                return False
            
            template = templates[template_id]
            
            # 删除图像文件
            if template.file_path and template.file_path.exists():
                try:
                    template.file_path.unlink()
                except Exception as e:
                    print(f"⚠️ 删除图像文件失败: {e}")
            
            # 从配置中移除
            del templates[template_id]
            
            # 保存更新后的配置
            self._save_templates_to_file(templates)
            
            return True
    
    @handle_template_errors
    def search_templates(self, criteria: TemplateSearchCriteria) -> List[Template]:
        """
        搜索模板
        
        Args:
            criteria: 搜索条件
            
        Returns:
            匹配的模板列表
        """
        templates = self.load_all_templates()
        results = []
        
        for template in templates.values():
            if self._matches_criteria(template, criteria):
                results.append(template)
        
        # 排序和分页
        results.sort(key=lambda t: t.metadata.updated_time, reverse=True)
        
        if criteria.offset > 0:
            results = results[criteria.offset:]
        
        if criteria.limit and criteria.limit > 0:
            results = results[:criteria.limit]
        
        return results
    
    @handle_template_errors
    def copy_image_to_storage(self, source_path: Path, template_id: str) -> Path:
        """
        复制图像文件到存储目录
        
        Args:
            source_path: 源文件路径
            template_id: 模板ID
            
        Returns:
            目标文件路径
        """
        if not source_path.exists():
            raise TemplateRepositoryError(
                str(source_path),
                "copy",
                "源文件不存在"
            )
        
        # 生成目标文件名
        file_ext = source_path.suffix
        target_path = self.images_dir / f"{template_id}{file_ext}"
        
        try:
            # 复制文件
            shutil.copy2(source_path, target_path)
            return target_path
        except Exception as e:
            raise TemplateRepositoryError(
                str(source_path),
                "copy",
                f"复制文件失败: {e}"
            )
    
    @handle_template_errors
    def create_backup(self) -> Path:
        """
        创建配置备份
        
        Returns:
            备份文件路径
        """
        if not self.config_file.exists():
            raise TemplateRepositoryError(
                str(self.config_file),
                "backup",
                "配置文件不存在"
            )
        
        # 生成备份文件名
        timestamp = int(time.time())
        backup_file = self.backup_dir / f"templates_backup_{timestamp}.{self.config_format}"
        
        try:
            shutil.copy2(self.config_file, backup_file)
            return backup_file
        except Exception as e:
            raise TemplateRepositoryError(
                str(self.config_file),
                "backup",
                f"创建备份失败: {e}"
            )
    
    @handle_template_errors
    def restore_from_backup(self, backup_path: Path) -> None:
        """
        从备份恢复配置
        
        Args:
            backup_path: 备份文件路径
        """
        if not backup_path.exists():
            raise TemplateRepositoryError(
                str(backup_path),
                "restore",
                "备份文件不存在"
            )
        
        try:
            # 创建当前配置的备份
            if self.config_file.exists():
                self.create_backup()
            
            # 恢复配置
            shutil.copy2(backup_path, self.config_file)
        except Exception as e:
            raise TemplateRepositoryError(
                str(backup_path),
                "restore",
                f"恢复备份失败: {e}"
            )
    
    def get_storage_stats(self) -> Dict[str, Any]:
        """获取存储统计信息"""
        try:
            templates = self.load_all_templates()
            
            # 计算图像文件总大小
            total_image_size = 0
            image_count = 0
            
            for image_file in self.images_dir.glob("*"):
                if image_file.is_file():
                    total_image_size += image_file.stat().st_size
                    image_count += 1
            
            return {
                'template_count': len(templates),
                'image_count': image_count,
                'total_image_size_mb': total_image_size / (1024 * 1024),
                'config_file_size_kb': self.config_file.stat().st_size / 1024 if self.config_file.exists() else 0,
                'storage_dir': str(self.storage_dir),
                'config_format': self.config_format
            }
        except Exception as e:
            return {'error': str(e)}
    
    def _save_templates_to_file(self, templates: Dict[str, Template]) -> None:
        """保存模板到文件"""
        try:
            # 创建备份
            if self.config_file.exists():
                self.create_backup()
            
            # 准备数据
            data = {
                'version': '1.0.0',
                'created_time': time.time(),
                'templates': [template.to_dict() for template in templates.values()]
            }
            
            # 写入文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                if self.config_format == 'json':
                    json.dump(data, f, ensure_ascii=False, indent=2)
                else:  # yaml
                    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
                    
        except Exception as e:
            raise TemplateRepositoryError(
                str(self.config_file),
                "save",
                f"保存配置文件失败: {e}"
            )
    
    def _matches_criteria(self, template: Template, criteria: TemplateSearchCriteria) -> bool:
        """检查模板是否匹配搜索条件"""
        # 名称模式匹配
        if criteria.name_pattern:
            if criteria.name_pattern.lower() not in template.name.lower():
                return False
        
        # 分类匹配
        if criteria.category and template.category != criteria.category:
            return False
        
        # 状态匹配
        if criteria.status and template.status != criteria.status:
            return False
        
        # 标签匹配
        if criteria.tags:
            template_tags = set(template.metadata.tags)
            criteria_tags = set(criteria.tags)
            if not criteria_tags.issubset(template_tags):
                return False
        
        # 时间范围匹配
        if criteria.created_after and template.metadata.created_time < criteria.created_after:
            return False
        
        if criteria.created_before and template.metadata.created_time > criteria.created_before:
            return False
        
        # 成功率匹配
        if criteria.min_success_rate and template.metadata.success_rate < criteria.min_success_rate:
            return False
        
        return True
