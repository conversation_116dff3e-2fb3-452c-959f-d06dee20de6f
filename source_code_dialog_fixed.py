#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源代码对话框 - 修复版本
显示与GUI完全相同的检测源代码
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QTextEdit, QPushButton, QLabel, QApplication, QMessageBox,
    QProgressDialog, QInputDialog, QFileDialog
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QIcon
from pathlib import Path
import sys
import subprocess
import tempfile
import os
import time


class CodeExecutionThread(QThread):
    """Code execution thread with proper UTF-8 encoding support"""
    output_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(int)

    def __init__(self, code: str):
        super().__init__()
        self.code = code

    def run(self):
        """Execute code with proper UTF-8 encoding handling"""
        temp_file = None
        try:
            # Create temporary file with UTF-8 encoding
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                f.write(self.code)
                temp_file = f.name

            # Execute the file
            process = subprocess.Popen(
                [sys.executable, temp_file],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace'
            )

            # Read output line by line
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.output_signal.emit(output.strip())

            return_code = process.poll()
            self.finished_signal.emit(return_code)

        except Exception as e:
            self.output_signal.emit(f"❌ 执行错误: {e}")
            self.finished_signal.emit(1)
        finally:
            # Clean up temporary file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except:
                    pass


class SourceCodeDialog(QDialog):
    """源代码对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置对话框图标
        self.setup_dialog_icon()
        self.parent_window = parent
        self.execution_thread = None

        # 存储GUI状态和静态快照代码
        self.current_gui_state = None
        self.static_snapshot_code = None

        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("📄 源代码编辑器")
        self.setGeometry(100, 100, 1200, 800)

        layout = QVBoxLayout()
        self.setLayout(layout)

        # 工具栏
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)

        # 生成当前状态代码按钮
        self.generate_snapshot_button = QPushButton("📸 生成当前状态代码")
        self.generate_snapshot_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.generate_snapshot_button.clicked.connect(self.generate_current_state_code)
        self.generate_snapshot_button.setToolTip("捕获当前GUI状态并生成固化参数的代码")
        toolbar_layout.addWidget(self.generate_snapshot_button)

        # 状态指示器
        self.state_indicator = QLabel("📋 通用模板代码")
        self.state_indicator.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        toolbar_layout.addWidget(self.state_indicator)

        toolbar_layout.addStretch()

        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # GUI检测方法复制标签页
        self.create_gui_detector_tab()
        
        # 使用示例标签页
        self.create_usage_example_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)

        # 运行代码按钮
        self.run_button = QPushButton("▶️ 运行代码")
        self.run_button.clicked.connect(self.run_current_code)
        button_layout.addWidget(self.run_button)

        # 复制代码按钮
        self.copy_button = QPushButton("📋 复制代码")
        self.copy_button.clicked.connect(self.copy_current_code)
        button_layout.addWidget(self.copy_button)

        # 保存代码按钮
        self.save_button = QPushButton("💾 保存代码")
        self.save_button.clicked.connect(self.save_current_code)
        button_layout.addWidget(self.save_button)

        # 导出独立脚本按钮
        self.export_button = QPushButton("📦 导出独立脚本")
        self.export_button.clicked.connect(self.export_as_standalone_script)
        button_layout.addWidget(self.export_button)

        button_layout.addStretch()

        # 关闭按钮
        self.close_button = QPushButton("❌ 关闭")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)

        # 输出区域
        self.output_text = QTextEdit()
        self.output_text.setMaximumHeight(200)
        self.output_text.setPlaceholderText("代码执行输出将显示在这里...")
        layout.addWidget(self.output_text)

    def setup_dialog_icon(self):
        """设置对话框图标"""
        try:
            from PyQt6.QtGui import QIcon

            # 图标文件路径
            icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_app.ico"

            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                print(f"✅ 源代码对话框图标已设置")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")

        except Exception as e:
            print(f"❌ 设置对话框图标失败: {e}")

    def create_gui_detector_tab(self):
        """创建GUI检测器标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 代码编辑器
        code_editor = QTextEdit()
        code_editor.setPlainText(self.get_gui_detector_code())
        code_editor.setFont(QFont("Consolas", 10))
        layout.addWidget(code_editor)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "🎯 GUI检测复制")
    
    def create_usage_example_tab(self):
        """创建使用示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 代码编辑器
        code_editor = QTextEdit()
        code_editor.setPlainText(self.get_usage_example_code())
        code_editor.setFont(QFont("Consolas", 10))
        layout.addWidget(code_editor)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "📚 使用示例")

    def get_gui_detector_code(self) -> str:
        """获取GUI检测器代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器 - GUI方法复制版本
完全复制GUI中的检测逻辑
"""

import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.absolute()
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

try:
    from yolo_opencv_detector.core.yolo_detector_v2 import YOLODetectorV2
    from yolo_opencv_detector.core.screen_capture_service_v2 import ScreenCaptureServiceV2
    from yolo_opencv_detector.utils.config_manager import ConfigManager
    print("✅ 所有模块导入成功")
except ImportError as e:
    print(f"❌ 模块导入失败: {e}")
    sys.exit(1)

def main():
    """主函数"""
    print("🎯 YOLO OpenCV检测器启动")
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化检测器
        detector = YOLODetectorV2(config_manager)
        
        # 初始化屏幕截图服务
        screen_capture = ScreenCaptureServiceV2()
        
        print("✅ 检测器初始化完成")
        
        # 执行检测
        image = screen_capture.capture_screen()
        detections = detector.detect(image)
        
        print(f"🎉 检测完成！发现 {len(detections)} 个目标")
        
        for i, detection in enumerate(detections):
            print(f"目标 {i+1}: {detection}")
            
    except Exception as e:
        print(f"❌ 检测失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''

    def get_usage_example_code(self) -> str:
        """获取使用示例代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器使用示例
展示各种检测和自动化操作
"""

print("🎯 YOLO检测器使用示例")
print("=" * 50)

print("📋 基础检测示例:")
print("1. 屏幕截图")
print("2. YOLO目标检测")
print("3. 结果处理")

print("\\n🤖 自动化操作示例:")
print("1. 智能点击")
print("2. 拖拽操作")
print("3. 多目标处理")

print("\\n✅ 示例代码运行成功！")
'''

    def generate_current_state_code(self):
        """生成当前GUI状态的代码"""
        print("🎯 生成当前状态代码功能暂未实现")
        QMessageBox.information(self, "提示", "当前状态代码生成功能正在开发中...")

    def run_current_code(self):
        """运行当前代码"""
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.widget(current_index)
            
            # 查找代码编辑器
            code_editor = current_widget.findChild(QTextEdit)
            if code_editor:
                code = code_editor.toPlainText()
                
                # 清空输出
                self.output_text.clear()
                self.output_text.appendPlainText("🚀 开始执行代码...")
                
                # 创建执行线程
                self.execution_thread = CodeExecutionThread(code)
                self.execution_thread.output_signal.connect(self.on_execution_output)
                self.execution_thread.finished_signal.connect(self.on_execution_finished)
                self.execution_thread.start()
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"运行代码失败: {e}")

    def on_execution_output(self, output: str):
        """处理执行输出"""
        self.output_text.appendPlainText(output)

    def on_execution_finished(self, return_code: int):
        """处理执行完成"""
        if return_code == 0:
            self.output_text.appendPlainText("\\n✅ 代码执行完成")
        else:
            self.output_text.appendPlainText(f"\\n❌ 代码执行失败 (返回码: {return_code})")

    def copy_current_code(self):
        """复制当前代码"""
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.widget(current_index)
            
            code_editor = current_widget.findChild(QTextEdit)
            if code_editor:
                code = code_editor.toPlainText()
                
                clipboard = QApplication.clipboard()
                clipboard.setText(code)
                
                QMessageBox.information(self, "成功", "代码已复制到剪贴板！")
                
        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制代码失败: {e}")

    def save_current_code(self):
        """保存当前代码"""
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.widget(current_index)
            
            code_editor = current_widget.findChild(QTextEdit)
            if code_editor:
                code = code_editor.toPlainText()
                
                file_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "保存代码",
                    "detection_code.py",
                    "Python文件 (*.py);;所有文件 (*)"
                )
                
                if file_path:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(code)
                    
                    QMessageBox.information(self, "成功", f"代码已保存到: {file_path}")
                    
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存代码失败: {e}")

    def export_as_standalone_script(self):
        """导出为独立脚本"""
        QMessageBox.information(self, "提示", "独立脚本导出功能正在开发中...")


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    dialog = SourceCodeDialog()
    dialog.show()
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
