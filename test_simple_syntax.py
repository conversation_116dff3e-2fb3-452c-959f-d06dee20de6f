#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单语法测试
"""

import sys
from pathlib import Path

def test_syntax_only():
    """只测试语法，不执行复杂功能"""
    try:
        print("🧪 测试语法修复...")
        
        # 添加项目路径
        project_root = Path(__file__).parent / "3_in_1" / "opencv_yolo" / "src"
        sys.path.insert(0, str(project_root))
        
        # 测试导入
        from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog
        print("✅ 源代码对话框导入成功")
        
        # 测试类定义
        print("✅ 类定义正确")
        
        # 测试方法存在
        if hasattr(SourceCodeDialog, '_generate_static_snapshot_code'):
            print("✅ _generate_static_snapshot_code 方法存在")
        
        if hasattr(SourceCodeDialog, '_generate_simple_detection_demo'):
            print("✅ _generate_simple_detection_demo 方法存在")
        
        print("🎉 语法修复测试通过！")
        return True
        
    except SyntaxError as e:
        print(f"❌ 语法错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    success = test_syntax_only()
    if success:
        print("\n✅ 语法修复成功！现在用户可以正常打开源代码对话框了！")
    else:
        print("\n❌ 语法修复失败！")
