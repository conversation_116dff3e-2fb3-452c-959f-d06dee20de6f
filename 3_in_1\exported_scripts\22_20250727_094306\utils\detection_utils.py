#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检测工具模块
提供检测结果处理和边界框操作的工具函数
"""

from typing import List, Tuple, Optional, Dict, Any
from dataclasses import dataclass
import numpy as np

@dataclass
class BoundingBox:
    """边界框数据类"""
    x: int
    y: int
    width: int
    height: int
    confidence: float = 0.0
    class_id: int = -1
    class_name: str = ""
    
    @property
    def x1(self) -> int:
        return self.x
    
    @property
    def y1(self) -> int:
        return self.y
    
    @property
    def x2(self) -> int:
        return self.x + self.width
    
    @property
    def y2(self) -> int:
        return self.y + self.height
    
    @property
    def center(self) -> Tuple[int, int]:
        return (self.x + self.width // 2, self.y + self.height // 2)
    
    @property
    def area(self) -> int:
        return self.width * self.height

@dataclass
class DetectionResult:
    """检测结果数据类"""
    bbox: BoundingBox
    confidence: float
    class_id: int
    class_name: str
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "bbox": {
                "x": self.bbox.x,
                "y": self.bbox.y,
                "width": self.bbox.width,
                "height": self.bbox.height
            },
            "confidence": self.confidence,
            "class_id": self.class_id,
            "class_name": self.class_name
        }

def calculate_iou(box1: BoundingBox, box2: BoundingBox) -> float:
    """计算两个边界框的IoU"""
    # 计算交集区域
    x1 = max(box1.x1, box2.x1)
    y1 = max(box1.y1, box2.y1)
    x2 = min(box1.x2, box2.x2)
    y2 = min(box1.y2, box2.y2)
    
    if x2 <= x1 or y2 <= y1:
        return 0.0
    
    intersection = (x2 - x1) * (y2 - y1)
    union = box1.area + box2.area - intersection
    
    return intersection / union if union > 0 else 0.0

def filter_detections_by_confidence(detections: List[DetectionResult], 
                                  threshold: float = 0.5) -> List[DetectionResult]:
    """根据置信度过滤检测结果"""
    return [det for det in detections if det.confidence >= threshold]

def sort_detections_by_confidence(detections: List[DetectionResult],
                                reverse: bool = True) -> List[DetectionResult]:
    """根据置信度排序检测结果"""
    return sorted(detections, key=lambda x: x.confidence, reverse=reverse)
