#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的源代码对话框 - 备用版本
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTextEdit, QPushButton,
    QLabel, QTabWidget, QWidget, QMessageBox
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QFont
from datetime import datetime

class SimpleSourceCodeDialog(QDialog):
    """简化的源代码对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_window = parent
        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("📄 源代码编辑器")
        self.setGeometry(100, 100, 1000, 700)
        
        layout = QVBoxLayout()
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 添加基础示例标签页
        self.create_basic_example_tab()
        
        # 添加简单检测标签页
        self.create_simple_detection_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        
        close_button = QPushButton("关闭")
        close_button.clicked.connect(self.close)
        button_layout.addStretch()
        button_layout.addWidget(close_button)
        
        layout.addLayout(button_layout)
        self.setLayout(layout)
    
    def create_basic_example_tab(self):
        """创建基础示例标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("🎯 基础检测示例")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        # 代码编辑器
        code_editor = QTextEdit()
        code_editor.setFont(QFont("Consolas", 10))
        code_editor.setPlainText(self.get_basic_example_code())
        code_editor.setReadOnly(True)
        
        layout.addWidget(code_editor)
        
        # 复制按钮
        copy_button = QPushButton("📋 复制代码")
        copy_button.clicked.connect(lambda: self.copy_code(code_editor.toPlainText()))
        layout.addWidget(copy_button)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "🎯 基础示例")
    
    def create_simple_detection_tab(self):
        """创建简单检测标签页"""
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 标题
        title_label = QLabel("🔍 简单检测演示")
        title_label.setStyleSheet("font-size: 14px; font-weight: bold; color: #2c3e50; margin: 10px;")
        layout.addWidget(title_label)
        
        # 代码编辑器
        code_editor = QTextEdit()
        code_editor.setFont(QFont("Consolas", 10))
        code_editor.setPlainText(self.get_simple_detection_code())
        code_editor.setReadOnly(True)
        
        layout.addWidget(code_editor)
        
        # 复制按钮
        copy_button = QPushButton("📋 复制代码")
        copy_button.clicked.connect(lambda: self.copy_code(code_editor.toPlainText()))
        layout.addWidget(copy_button)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "🔍 简单检测")
    
    def get_basic_example_code(self):
        """获取基础示例代码"""
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO OpenCV检测器 - 基础示例
"""

import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.absolute()
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

def main():
    """主函数"""
    print("🎯 YOLO OpenCV检测器 - 基础示例")
    
    try:
        # 导入检测器组件
        from yolo_opencv_detector.core.yolo_detector_v2 import YOLODetectorV2
        from yolo_opencv_detector.core.screen_capture_service_v2 import ScreenCaptureServiceV2
        from yolo_opencv_detector.utils.config_manager import ConfigManager
        
        print("✅ 模块导入成功")
        
        # 初始化组件
        config_manager = ConfigManager()
        yolo_detector = YOLODetectorV2(config_manager)
        screen_capture = ScreenCaptureServiceV2()
        
        print("✅ 组件初始化完成")
        
        # 执行屏幕检测
        image = screen_capture.capture_fullscreen()
        if image is not None:
            detections = yolo_detector.detect(image, confidence=0.5)
            print(f"✅ 检测完成，发现 {len(detections)} 个目标")
        else:
            print("❌ 屏幕截图失败")
            
    except Exception as e:
        print(f"❌ 执行失败: {e}")

if __name__ == "__main__":
    main()
'''
    
    def get_simple_detection_code(self):
        """获取简单检测代码"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        return f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单检测演示代码
生成时间: {timestamp}
"""

print("🎯 简单检测演示")
print("📊 使用参数:")
print("   • 置信度阈值: 0.50")
print("   • NMS阈值: 0.45")
print("   • 选择模板: 无")
print("✅ 演示代码运行成功！")

# 这是一个简化的演示版本
# 完整的检测功能请参考基础示例标签页
'''
    
    def copy_code(self, code_text):
        """复制代码到剪贴板"""
        try:
            from PyQt6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(code_text)
            
            QMessageBox.information(
                self,
                "复制成功",
                "代码已复制到剪贴板！"
            )
        except Exception as e:
            QMessageBox.critical(
                self,
                "复制失败",
                f"复制代码失败: {e}"
            )

# 测试函数
def main():
    """测试函数"""
    from PyQt6.QtWidgets import QApplication
    import sys
    
    app = QApplication(sys.argv)
    dialog = SimpleSourceCodeDialog()
    dialog.show()
    sys.exit(app.exec())

if __name__ == "__main__":
    main()
