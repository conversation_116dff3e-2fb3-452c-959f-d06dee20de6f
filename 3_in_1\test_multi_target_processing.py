#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立测试多目标处理功能
作者: Cursor AI
创建时间: 2025-07-27
"""

def process_multiple_targets(detections):
    """
    多目标检测结果处理机制 - 独立版本
    
    Args:
        detections: 检测结果列表
        
    Returns:
        dict: 包含各种目标选择策略的结果
    """
    if not detections:
        print("   ⚠️ 没有检测到目标")
        return {"all_targets": [], "selected_target": None, "selection_strategy": "none"}
    
    # 转换检测结果为统一格式
    targets = []
    for i, detection in enumerate(detections):
        bbox = detection.get('bbox', [0,0,0,0])
        confidence = detection.get('confidence', 0.0)
        class_name = detection.get('class_name', 'unknown')
        
        # 计算面积和中心点
        if len(bbox) == 4:
            x, y, w, h = bbox
            area = w * h
            center_x = x + w // 2
            center_y = y + h // 2
        else:
            area = 0
            center_x, center_y = 0, 0
        
        target = {
            "index": i + 1,
            "bbox": bbox,
            "confidence": confidence,
            "class_name": class_name,
            "area": area,
            "center": (center_x, center_y),
            "click_point": (center_x, center_y)  # 默认点击中心点
        }
        targets.append(target)
    
    print(f"   📊 检测到 {len(targets)} 个目标")
    
    # 输出所有目标的详细坐标信息
    print("\n📍 所有目标坐标信息:")
    for target in targets:
        bbox = target["bbox"]
        center = target["center"]
        print(f"   目标{target['index']}: {target['class_name']}")
        print(f"      边界框: {bbox} (x,y,w,h)")
        print(f"      中心点: {center} (点击坐标)")
        print(f"      面积: {target['area']} 像素")
        print(f"      置信度: {target['confidence']:.3f}")
    
    # 目标选择策略
    strategies = {}
    
    # 1. 第一个目标
    strategies["first"] = targets[0]
    print(f"\n🎯 目标选择策略:")
    print(f"   1️⃣ 第一个目标: {strategies['first']['class_name']} (置信度: {strategies['first']['confidence']:.3f})")
    
    # 2. 置信度最高的目标
    strategies["highest_confidence"] = max(targets, key=lambda x: x["confidence"])
    print(f"   🏆 置信度最高: {strategies['highest_confidence']['class_name']} (置信度: {strategies['highest_confidence']['confidence']:.3f})")
    
    # 3. 面积最大的目标
    strategies["largest_area"] = max(targets, key=lambda x: x["area"])
    print(f"   📏 面积最大: {strategies['largest_area']['class_name']} (面积: {strategies['largest_area']['area']} 像素)")
    
    # 4. 按置信度排序的所有目标
    sorted_by_confidence = sorted(targets, key=lambda x: x["confidence"], reverse=True)
    strategies["sorted_by_confidence"] = sorted_by_confidence
    confidence_list = [f'{t["class_name"]}({t["confidence"]:.2f})' for t in sorted_by_confidence[:3]]
    print(f"   📋 按置信度排序: {', '.join(confidence_list)}")

    # 5. 按面积排序的所有目标
    sorted_by_area = sorted(targets, key=lambda x: x["area"], reverse=True)
    strategies["sorted_by_area"] = sorted_by_area
    area_list = [f'{t["class_name"]}({t["area"]})' for t in sorted_by_area[:3]]
    print(f"   📐 按面积排序: {', '.join(area_list)}")
    
    # 默认选择策略：置信度最高
    selected_target = strategies["highest_confidence"]
    
    print(f"\n✅ 推荐目标: {selected_target['class_name']} (置信度最高)")
    print(f"   🖱️ 建议点击坐标: {selected_target['click_point']}")
    print(f"   📦 边界框: {selected_target['bbox']}")
    
    # 输出自动化脚本可用的坐标信息
    print(f"\n🤖 自动化脚本坐标信息:")
    print(f"   CLICK_X = {selected_target['click_point'][0]}")
    print(f"   CLICK_Y = {selected_target['click_point'][1]}")
    print(f"   BBOX_X = {selected_target['bbox'][0]}")
    print(f"   BBOX_Y = {selected_target['bbox'][1]}")
    print(f"   BBOX_W = {selected_target['bbox'][2]}")
    print(f"   BBOX_H = {selected_target['bbox'][3]}")
    
    return {
        "all_targets": targets,
        "selected_target": selected_target,
        "strategies": strategies,
        "selection_strategy": "highest_confidence"
    }

def test_multi_target_scenarios():
    """测试多种目标场景"""
    print("🧪 测试多目标处理功能")
    print("=" * 50)
    
    # 场景1: 多个不同类型目标
    print("\n📋 场景1: 多个不同类型目标")
    print("-" * 30)
    
    detections1 = [
        {"bbox": [100, 100, 50, 30], "confidence": 0.85, "class_name": "本地磁盘"},
        {"bbox": [200, 150, 60, 40], "confidence": 0.92, "class_name": "文件夹"},
        {"bbox": [300, 200, 40, 25], "confidence": 0.78, "class_name": "文档"}
    ]
    
    result1 = process_multiple_targets(detections1)
    
    # 场景2: 相同类型多个目标
    print("\n📋 场景2: 相同类型多个目标")
    print("-" * 30)
    
    detections2 = [
        {"bbox": [50, 50, 30, 30], "confidence": 0.88, "class_name": "按钮"},
        {"bbox": [150, 100, 35, 25], "confidence": 0.95, "class_name": "按钮"},
        {"bbox": [250, 150, 28, 32], "confidence": 0.82, "class_name": "按钮"}
    ]
    
    result2 = process_multiple_targets(detections2)
    
    # 场景3: 单个目标
    print("\n📋 场景3: 单个目标")
    print("-" * 30)
    
    detections3 = [
        {"bbox": [120, 80, 45, 35], "confidence": 0.91, "class_name": "登录按钮"}
    ]
    
    result3 = process_multiple_targets(detections3)
    
    # 场景4: 无目标
    print("\n📋 场景4: 无目标")
    print("-" * 30)
    
    result4 = process_multiple_targets([])
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    scenarios = [
        ("多个不同类型目标", result1),
        ("相同类型多个目标", result2),
        ("单个目标", result3),
        ("无目标", result4)
    ]
    
    for name, result in scenarios:
        if result and result.get("selected_target"):
            selected = result["selected_target"]
            print(f"✅ {name}: 推荐 {selected['class_name']} 坐标{selected['click_point']}")
        elif result and result.get("selection_strategy") == "none":
            print(f"✅ {name}: 正确处理无目标情况")
        else:
            print(f"❌ {name}: 处理异常")

if __name__ == "__main__":
    print("🚀 开始测试多目标处理功能...")
    test_multi_target_scenarios()
    print("\n🎉 多目标处理功能测试完成！")
