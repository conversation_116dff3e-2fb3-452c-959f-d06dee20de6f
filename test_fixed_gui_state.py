#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的GUI状态捕获功能
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# 添加项目路径
project_root = Path(__file__).parent / "3_in_1" / "opencv_yolo" / "src"
sys.path.insert(0, str(project_root))

def test_simple_static_code():
    """测试简化的静态快照代码生成"""
    try:
        print("🧪 测试简化的静态快照代码生成...")
        
        # 模拟GUI状态
        gui_state = {
            'confidence_threshold': 0.75,
            'nms_threshold': 0.45,
            'selected_template': '测试模板',
            'template_path': None,
            'detection_interval': 2.0,
            'export_timestamp': datetime.now().isoformat(),
            'auto_detect': True,
            'capture_method': 'test'
        }
        
        # 生成简化的静态快照代码
        template_info = f"'{gui_state['selected_template']}'" if gui_state['selected_template'] else "None"
        
        static_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（简化版本）
导出时间: {gui_state['export_timestamp']}

此脚本是导出时刻GUI状态的完全静态快照，包含以下固化参数：
- 置信度阈值: {gui_state['confidence_threshold']:.2f}
- NMS阈值: {gui_state['nms_threshold']:.2f}
- 选择模板: {gui_state['selected_template'] or '无'}
- 检测间隔: {gui_state['detection_interval']:.1f}秒
- 自动检测: {gui_state['auto_detect']}

注意：此脚本不会动态读取GUI配置，参数已固化。
如需不同参数，请重新生成新的状态快照。
"""

import sys
import os
from pathlib import Path

# 基础导入
try:
    import numpy as np
    import cv2
    from datetime import datetime
    print("✅ 基础模块导入成功")
except ImportError as e:
    print(f"❌ 导入错误: {{e}}")
    print("请确保安装了必要的依赖包：pip install numpy opencv-python")
    sys.exit(1)

# ============================================================================
# 导出时刻的固化参数（静态快照）
# ============================================================================
EXPORTED_CONFIDENCE_THRESHOLD = {gui_state['confidence_threshold']:.3f}
EXPORTED_NMS_THRESHOLD = {gui_state['nms_threshold']:.3f}
EXPORTED_TEMPLATE_NAME = {template_info}
EXPORTED_DETECTION_INTERVAL = {gui_state['detection_interval']:.1f}
EXPORTED_AUTO_DETECT = {gui_state['auto_detect']}
EXPORTED_TIMESTAMP = "{gui_state['export_timestamp']}"

def main():
    """主函数 - 简化版本"""
    print("🎯 YOLO检测器 - 静态快照版本（简化版）")
    print(f"📅 导出时间: {{EXPORTED_TIMESTAMP}}")
    print("⚙️  固化参数:")
    print(f"   • 置信度阈值: {{EXPORTED_CONFIDENCE_THRESHOLD}}")
    print(f"   • NMS阈值: {{EXPORTED_NMS_THRESHOLD}}")
    print(f"   • 选择模板: {{EXPORTED_TEMPLATE_NAME}}")
    print(f"   • 检测间隔: {{EXPORTED_DETECTION_INTERVAL}}秒")
    print(f"   • 自动检测: {{EXPORTED_AUTO_DETECT}}")
    
    print("\\n✅ 静态快照代码运行成功！")
    print("💡 这是一个简化版本，展示了导出时刻的GUI状态参数。")
    print("🔧 如需完整的检测功能，请使用导出的独立脚本。")

if __name__ == "__main__":
    main()
'''
        
        print("✅ 简化静态快照代码生成成功")
        print(f"📝 代码长度: {len(static_code)} 字符")
        
        # 保存测试代码到文件
        test_output_path = Path("test_fixed_static_snapshot.py")
        with open(test_output_path, 'w', encoding='utf-8') as f:
            f.write(static_code)
        
        print(f"💾 测试代码已保存到: {test_output_path}")
        
        # 执行测试代码
        print("🚀 执行测试代码...")
        exec(static_code)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_simple_static_code()
    if success:
        print("\n🎉 修复后的GUI状态捕获功能测试通过！")
    else:
        print("\n💥 修复后的GUI状态捕获功能测试失败！")
