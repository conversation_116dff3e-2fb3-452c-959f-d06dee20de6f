# YOLO目标检测独立脚本依赖包
# 安装命令: pip install -r requirements.txt

# 核心依赖
opencv-python>=4.5.0
numpy>=1.19.0
Pillow>=8.0.0

# GUI相关（如果需要）
PyQt6>=6.0.0

# 机器学习相关
ultralytics>=8.0.0

# 工具库
pathlib2>=2.3.0
python-dateutil>=2.8.0

# 日志和配置
colorlog>=6.0.0

# 可选依赖（用于增强功能）
matplotlib>=3.3.0
scipy>=1.7.0
scikit-image>=0.18.0

# 注意事项:
# 1. 如果遇到安装问题，可以尝试使用国内镜像源:
#    pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
# 2. 某些包可能需要特定版本，请根据实际情况调整
# 3. 如果不需要GUI功能，可以注释掉PyQt6相关依赖
