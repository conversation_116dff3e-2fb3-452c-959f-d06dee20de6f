#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试可工作的修复版本
"""

from datetime import datetime

def test_working_static_code():
    """测试可工作的静态快照代码"""
    try:
        print("🧪 测试可工作的静态快照代码...")
        
        # 模拟GUI状态
        gui_state = {
            'confidence_threshold': 0.75,
            'nms_threshold': 0.45,
            'selected_template': '测试模板',
            'detection_interval': 2.0,
            'export_timestamp': datetime.now().isoformat(),
            'auto_detect': True
        }
        
        # 生成可工作的静态快照代码（避免f-string中的双花括号问题）
        template_info = f"'{gui_state['selected_template']}'" if gui_state['selected_template'] else "None"
        
        # 使用字符串拼接而不是f-string来避免花括号问题
        static_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（可工作版本）
导出时间: ''' + gui_state['export_timestamp'] + '''
"""

from datetime import datetime

# 导出时刻的固化参数
EXPORTED_CONFIDENCE_THRESHOLD = ''' + str(gui_state['confidence_threshold']) + '''
EXPORTED_NMS_THRESHOLD = ''' + str(gui_state['nms_threshold']) + '''
EXPORTED_TEMPLATE_NAME = ''' + template_info + '''
EXPORTED_DETECTION_INTERVAL = ''' + str(gui_state['detection_interval']) + '''
EXPORTED_AUTO_DETECT = ''' + str(gui_state['auto_detect']) + '''
EXPORTED_TIMESTAMP = "''' + gui_state['export_timestamp'] + '''"

def main():
    """主函数"""
    print("🎯 YOLO检测器 - 静态快照版本（可工作版）")
    print("📅 导出时间: " + EXPORTED_TIMESTAMP)
    print("⚙️  固化参数:")
    print("   • 置信度阈值: " + str(EXPORTED_CONFIDENCE_THRESHOLD))
    print("   • NMS阈值: " + str(EXPORTED_NMS_THRESHOLD))
    print("   • 选择模板: " + str(EXPORTED_TEMPLATE_NAME))
    print("   • 检测间隔: " + str(EXPORTED_DETECTION_INTERVAL) + "秒")
    print("   • 自动检测: " + str(EXPORTED_AUTO_DETECT))
    print("\\n✅ 静态快照代码运行成功！")

if __name__ == "__main__":
    main()
'''
        
        print("✅ 可工作的静态快照代码生成成功")
        print(f"📝 代码长度: {len(static_code)} 字符")
        
        # 保存代码到文件
        with open("working_static_snapshot.py", 'w', encoding='utf-8') as f:
            f.write(static_code)
        print("💾 代码已保存到 working_static_snapshot.py")
        
        # 执行测试代码
        print("🚀 执行测试代码...")
        exec(static_code)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_working_static_code()
    if success:
        print("\n🎉 可工作版本测试通过！")
    else:
        print("\n💥 可工作版本测试失败！")
