#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板数据模型定义

定义了模板管理系统中使用的所有数据结构，提供统一的数据模型和序列化支持。

Created: 2025-07-27
Author: Augment Agent
"""

import time
import uuid
from dataclasses import dataclass, field, asdict
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from enum import Enum
import numpy as np


class TemplateCategory(Enum):
    """模板分类枚举"""
    GENERAL = "通用"
    BUTTON = "按钮"
    ICON = "图标"
    TEXT = "文本"
    INPUT = "输入框"
    MENU = "菜单"
    DIALOG = "对话框"
    OTHER = "其他"


class TemplateStatus(Enum):
    """模板状态枚举"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    DEPRECATED = "deprecated"
    DRAFT = "draft"


@dataclass(frozen=True)
class TemplateMetadata:
    """模板元数据"""
    created_time: float = field(default_factory=time.time)
    updated_time: float = field(default_factory=time.time)
    created_by: str = "system"
    updated_by: str = "system"
    version: str = "1.0.0"
    usage_count: int = 0
    success_rate: float = 0.0
    last_used_time: Optional[float] = None
    tags: List[str] = field(default_factory=list)
    custom_fields: Dict[str, Any] = field(default_factory=dict)


@dataclass(frozen=True)
class TemplateImageInfo:
    """模板图像信息"""
    width: int
    height: int
    channels: int
    format: str
    file_size: int
    checksum: str
    
    @classmethod
    def from_image(cls, image: np.ndarray, file_path: Path) -> 'TemplateImageInfo':
        """从图像数组创建图像信息"""
        height, width = image.shape[:2]
        channels = image.shape[2] if len(image.shape) > 2 else 1
        file_size = file_path.stat().st_size if file_path.exists() else 0
        
        # 计算图像校验和
        import hashlib
        checksum = hashlib.md5(image.tobytes()).hexdigest()
        
        return cls(
            width=width,
            height=height,
            channels=channels,
            format=file_path.suffix.lower(),
            file_size=file_size,
            checksum=checksum
        )


@dataclass(frozen=True)
class Template:
    """模板数据模型 - 不可变"""
    
    # 基本信息
    id: str
    name: str
    display_name: str
    description: str
    category: TemplateCategory
    status: TemplateStatus = TemplateStatus.ACTIVE
    
    # 文件信息
    file_path: Path = field(default_factory=lambda: Path())
    relative_path: str = ""
    
    # 图像信息
    image_info: Optional[TemplateImageInfo] = None
    
    # 检测参数
    threshold: float = 0.8
    method: str = "TM_CCOEFF_NORMED"
    
    # 元数据
    metadata: TemplateMetadata = field(default_factory=TemplateMetadata)
    
    def __post_init__(self):
        """后初始化验证"""
        if not self.id:
            object.__setattr__(self, 'id', str(uuid.uuid4()))
        
        if not self.display_name:
            object.__setattr__(self, 'display_name', self.name)
        
        if not self.relative_path and self.file_path:
            object.__setattr__(self, 'relative_path', str(self.file_path))
    
    @property
    def clean_name(self) -> str:
        """获取清理后的模板名称（移除分类前缀）"""
        if '] ' in self.name:
            return self.name.split('] ', 1)[1]
        return self.name
    
    @property
    def full_display_name(self) -> str:
        """获取完整显示名称（包含分类前缀）"""
        if self.category and not self.name.startswith('['):
            return f"[{self.category.value}] {self.name}"
        return self.display_name or self.name
    
    @property
    def is_valid(self) -> bool:
        """检查模板是否有效"""
        return (
            bool(self.id) and
            bool(self.name) and
            self.file_path.exists() if self.file_path else True
        )
    
    def to_dict(self, include_metadata: bool = True) -> Dict[str, Any]:
        """转换为字典（用于序列化）"""
        data = {
            'id': self.id,
            'name': self.name,
            'display_name': self.display_name,
            'description': self.description,
            'category': self.category.value,
            'status': self.status.value,
            'file_path': str(self.file_path),
            'relative_path': self.relative_path,
            'threshold': self.threshold,
            'method': self.method,
        }
        
        if self.image_info:
            data['image_info'] = asdict(self.image_info)
        
        if include_metadata:
            data['metadata'] = asdict(self.metadata)
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Template':
        """从字典创建模板对象"""
        # 处理枚举类型
        category = TemplateCategory(data.get('category', TemplateCategory.GENERAL.value))
        status = TemplateStatus(data.get('status', TemplateStatus.ACTIVE.value))
        
        # 处理路径
        file_path = Path(data.get('file_path', ''))
        
        # 处理图像信息
        image_info = None
        if 'image_info' in data:
            image_info = TemplateImageInfo(**data['image_info'])
        
        # 处理元数据
        metadata = TemplateMetadata()
        if 'metadata' in data:
            metadata = TemplateMetadata(**data['metadata'])
        
        return cls(
            id=data.get('id', str(uuid.uuid4())),
            name=data['name'],
            display_name=data.get('display_name', data['name']),
            description=data.get('description', ''),
            category=category,
            status=status,
            file_path=file_path,
            relative_path=data.get('relative_path', str(file_path)),
            image_info=image_info,
            threshold=data.get('threshold', 0.8),
            method=data.get('method', 'TM_CCOEFF_NORMED'),
            metadata=metadata
        )
    
    def update(self, **kwargs) -> 'Template':
        """创建更新后的模板副本（保持不可变性）"""
        current_data = self.to_dict()
        current_data.update(kwargs)
        
        # 更新元数据中的修改时间
        if 'metadata' not in kwargs:
            metadata_dict = asdict(self.metadata)
            metadata_dict['updated_time'] = time.time()
            current_data['metadata'] = metadata_dict
        
        return Template.from_dict(current_data)


@dataclass
class TemplateSearchCriteria:
    """模板搜索条件"""
    name_pattern: Optional[str] = None
    category: Optional[TemplateCategory] = None
    status: Optional[TemplateStatus] = None
    tags: Optional[List[str]] = None
    created_after: Optional[float] = None
    created_before: Optional[float] = None
    min_success_rate: Optional[float] = None
    limit: Optional[int] = None
    offset: int = 0


@dataclass
class TemplateOperationResult:
    """模板操作结果"""
    success: bool
    template: Optional[Template] = None
    error_message: Optional[str] = None
    error_code: Optional[str] = None
    details: Dict[str, Any] = field(default_factory=dict)
