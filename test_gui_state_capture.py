#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试GUI状态捕获功能
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent / "3_in_1" / "opencv_yolo" / "src"
sys.path.insert(0, str(project_root))

def test_gui_state_capture():
    """测试GUI状态捕获功能"""
    try:
        print("🧪 开始测试GUI状态捕获功能...")
        
        # 导入必要模块
        from datetime import datetime
        
        # 创建模拟的GUI状态
        mock_gui_state = {
            'confidence_threshold': 0.5,
            'nms_threshold': 0.4,
            'selected_template': None,
            'template_path': None,
            'detection_interval': 1.0,
            'export_timestamp': datetime.now().isoformat(),
            'auto_detect': False,
            'capture_method': 'test'
        }
        
        print("✅ 模拟GUI状态创建成功:")
        for key, value in mock_gui_state.items():
            print(f"   • {key}: {value}")
        
        # 测试静态快照代码生成
        template_info = f"'{mock_gui_state['selected_template']}'" if mock_gui_state['selected_template'] else "None"
        
        static_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（测试版本）
导出时间: {mock_gui_state['export_timestamp']}

此脚本是导出时刻GUI状态的完全静态快照，包含以下固化参数：
- 置信度阈值: {mock_gui_state['confidence_threshold']:.2f}
- NMS阈值: {mock_gui_state['nms_threshold']:.2f}
- 选择模板: {mock_gui_state['selected_template'] or '无'}
- 检测间隔: {mock_gui_state['detection_interval']:.1f}秒
- 自动检测: {mock_gui_state['auto_detect']}

注意：此脚本不会动态读取GUI配置，参数已固化。
如需不同参数，请重新生成新的状态快照。
"""

# ============================================================================
# 导出时刻的固化参数（静态快照）
# ============================================================================
EXPORTED_CONFIDENCE_THRESHOLD = {mock_gui_state['confidence_threshold']:.3f}
EXPORTED_NMS_THRESHOLD = {mock_gui_state['nms_threshold']:.3f}
EXPORTED_TEMPLATE_NAME = {template_info}
EXPORTED_DETECTION_INTERVAL = {mock_gui_state['detection_interval']:.1f}
EXPORTED_AUTO_DETECT = {mock_gui_state['auto_detect']}
EXPORTED_TIMESTAMP = "{mock_gui_state['export_timestamp']}"

print(f"🎯 YOLO检测器 - 静态快照版本（测试）")
print(f"📅 导出时间: {{EXPORTED_TIMESTAMP}}")
print(f"⚙️  固化参数:")
print(f"   • 置信度阈值: {{EXPORTED_CONFIDENCE_THRESHOLD}}")
print(f"   • NMS阈值: {{EXPORTED_NMS_THRESHOLD}}")
print(f"   • 选择模板: {{EXPORTED_TEMPLATE_NAME}}")
print(f"   • 检测间隔: {{EXPORTED_DETECTION_INTERVAL}}秒")
print(f"   • 自动检测: {{EXPORTED_AUTO_DETECT}}")

def main():
    print("✅ 静态快照代码测试成功！")

if __name__ == "__main__":
    main()
'''
        
        print("✅ 静态快照代码生成成功")
        print(f"📝 代码长度: {len(static_code)} 字符")
        
        # 保存测试代码到文件
        test_output_path = Path("test_static_snapshot.py")
        with open(test_output_path, 'w', encoding='utf-8') as f:
            f.write(static_code)
        
        print(f"💾 测试代码已保存到: {test_output_path}")
        
        # 执行测试代码
        print("🚀 执行测试代码...")
        exec(static_code)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_gui_state_capture()
    if success:
        print("\n🎉 GUI状态捕获功能测试通过！")
    else:
        print("\n💥 GUI状态捕获功能测试失败！")
