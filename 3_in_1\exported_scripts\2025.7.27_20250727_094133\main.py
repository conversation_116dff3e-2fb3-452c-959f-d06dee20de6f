#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
2025.7.27 - 独立运行脚本
基于YOLO的目标检测自动化脚本

自动生成时间: 2025-07-27 09:41:33
运行环境: Python 3.7+
依赖包: 请先运行 pip install -r requirements.txt

使用方法:
1. 确保已安装所有依赖包
2. 运行 python main.py 或双击 run.bat
3. 按照提示进行操作
"""

import sys
import os
import cv2
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入工具模块
try:
    from utils.chinese_text_renderer import put_chinese_text, ChineseTextRenderer
    CHINESE_RENDERER_AVAILABLE = True
except ImportError:
    CHINESE_RENDERER_AVAILABLE = False
    print("⚠️ 中文渲染器不可用，中文字符可能显示为问号")

try:
    from utils.detection_utils import DetectionResult, BoundingBox
    DETECTION_UTILS_AVAILABLE = True
except ImportError:
    DETECTION_UTILS_AVAILABLE = False
    print("⚠️ 检测工具不可用，使用基础功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('detection_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StandaloneDetector:
    """独立检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.logger = logger
        self.config = self._load_config()
        self.chinese_renderer = None
        
        if CHINESE_RENDERER_AVAILABLE:
            try:
                self.chinese_renderer = ChineseTextRenderer()
                self.logger.info("✅ 中文渲染器初始化成功")
            except Exception as e:
                self.logger.warning(f"中文渲染器初始化失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = Path("config/settings.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载配置失败: {e}")
        
        # 返回默认配置
        return {
            "template_name": "2025.7.27",
            "description": "基于YOLO的目标检测自动化脚本",
            "confidence_threshold": 0.5,
            "nms_threshold": 0.4
        }
    
    def render_text_on_image(self, image: np.ndarray, text: str, 
                           position: Tuple[int, int], 
                           color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """在图像上渲染文字"""
        if self.chinese_renderer and CHINESE_RENDERER_AVAILABLE:
            try:
                return put_chinese_text(image, text, position, 
                                      font_size=16, color=color, background=True)
            except Exception as e:
                self.logger.warning(f"中文渲染失败: {e}")
        
        # 回退到OpenCV渲染
        cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                   0.6, color, 2, cv2.LINE_AA)
        return image
    
    def run_detection(self):
        """运行检测主程序"""
        self.logger.info("🚀 开始运行检测程序...")
        self.logger.info(f"📋 模板: {self.config.get('template_name', 'Unknown')}")
        self.logger.info(f"📝 描述: {self.config.get('description', 'No description')}")
        
        try:
            # 这里插入用户的检测代码
                        #!/usr/bin/env python3
            # -*- coding: utf-8 -*-
            """
            YOLO OpenCV检测器自动化操作完整使用示例
            演示如何使用检测结果进行智能自动化操作
            """

            import sys
            import time
            import pyautogui
            from pathlib import Path

            # 添加项目路径
            project_root = Path(__file__).parent.parent.parent
            sys.path.insert(0, str(project_root / "src"))

            def office_automation_example():
                """办公软件自动化示例"""
                print("🏢 办公软件自动化示例")
                print("=" * 50)

                # 1. 初始化检测器
                from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

                class OfficeAutomator(SourceCodeDialog):
                    def __init__(self):
                        super().__init__()
                        self.load_gui_config()

                automator = OfficeAutomator()

                # 2. 执行屏幕检测
                print("🔍 执行屏幕检测...")
                results = automator.gui_detect_screen()

                if not results:
                    print("❌ 未检测到任何目标")
                    return False

                # 3. 解析检测结果
                targets = automator.parse_detection_results(results)
                print(f"✅ 检测到 {len(targets)} 个目标")

                # 4. 智能目标选择演示
                print("\n🎯 智能目标选择演示:")

                # 按置信度选择最佳目标
                best_target = automator.select_target_by_confidence(targets, 'highest')
                if best_target:
                    print(f"  最佳目标: {best_target.get('class_name', 'unknown')} (置信度: {best_target.get('confidence', 0):.3f})")

                    # 获取目标坐标
                    center_coords = automator.get_target_coordinates(best_target, 'center')
                    print(f"  中心坐标: ({center_coords['x']}, {center_coords['y']})")

                    # 坐标验证
                    if automator.validate_coordinates(center_coords):
                        print("  ✅ 坐标验证通过")

                        # 执行点击操作
                        print("\n🖱️ 执行自动化操作:")
                        success = automator.perform_mouse_click(center_coords, 'left', 'single', 0.5)
                        if success:
                            print("  ✅ 鼠标点击成功")
                            automator.log_automation_action('mouse_click', center_coords, success)
                        else:
                            print("  ❌ 鼠标点击失败")
                    else:
                        print("  ❌ 坐标验证失败")

                return True

            def multi_target_operation_example():
                """多目标操作示例"""
                print("\n🎯 多目标操作示例")
                print("=" * 50)

                # 初始化自动化器
                from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

                class MultiTargetAutomator(SourceCodeDialog):
                    def __init__(self):
                        super().__init__()
                        self.load_gui_config()

                automator = MultiTargetAutomator()

                # 执行检测
                results = automator.gui_detect_screen()
                targets = automator.parse_detection_results(results)

                if len(targets) < 2:
                    print("⚠️ 需要至少2个目标进行多目标操作")
                    return False

                print(f"✅ 找到 {len(targets)} 个目标")

                # 多目标选择策略
                print("\n📍 多目标选择策略:")

                # 选择最左和最右的目标
                leftmost = automator.select_target_by_position(targets, 'leftmost')
                rightmost = automator.select_target_by_position(targets, 'rightmost')

                if leftmost and rightmost:
                    left_coords = automator.get_target_coordinates(leftmost, 'center')
                    right_coords = automator.get_target_coordinates(rightmost, 'center')

                    print(f"  最左目标: ({left_coords['x']}, {left_coords['y']})")
                    print(f"  最右目标: ({right_coords['x']}, {right_coords['y']})")

                    # 执行拖拽操作
                    print("\n🔄 执行拖拽操作:")
                    drag_success = automator.perform_mouse_drag(
                        left_coords, right_coords, 1.0, 'left'
                    )

                    if drag_success:
                        print("  ✅ 拖拽操作成功")
                    else:
                        print("  ❌ 拖拽操作失败")

                return True

            def target_selection_strategies_example():
                """目标选择策略示例"""
                print("\n🔍 目标选择策略示例")
                print("=" * 50)

                # 初始化自动化器
                from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

                class TargetSelector(SourceCodeDialog):
                    def __init__(self):
                        super().__init__()
                        self.load_gui_config()

                selector = TargetSelector()

                # 执行检测
                results = selector.gui_detect_screen()
                targets = selector.parse_detection_results(results)

                if not targets:
                    print("❌ 未检测到目标")
                    return False

                print(f"✅ 检测到 {len(targets)} 个目标")

                # 演示各种选择策略
                print("\n📊 选择策略演示:")

                # 1. 按置信度选择
                highest_conf = selector.select_target_by_confidence(targets, 'highest')
                if highest_conf:
                    print(f"  最高置信度: {highest_conf.get('confidence', 0):.3f}")

                # 2. 按位置选择
                center_target = selector.select_target_by_position(targets, 'center')
                if center_target:
                    coords = selector.get_target_coordinates(center_target, 'center')
                    print(f"  中心目标: ({coords['x']}, {coords['y']})")

                # 3. 按大小选择
                largest_target = selector.select_target_by_size(targets, 'largest')
                if largest_target:
                    dimensions = selector.calculate_target_area_and_dimensions(largest_target)
                    print(f"  最大目标面积: {dimensions.get('area', 0)} 像素")

                # 4. 自定义条件选择
                def high_confidence_condition(target):
                    return target.get('confidence', 0) > 0.8

                high_conf_targets = selector.select_target_by_custom_condition(targets, high_confidence_condition)
                print(f"  高置信度目标数量: {len(high_conf_targets)}")

                return True

            def error_handling_example():
                """错误处理机制示例"""
                print("\n🛡️ 错误处理机制示例")
                print("=" * 50)

                # 初始化自动化器
                from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog

                class SafeAutomator(SourceCodeDialog):
                    def __init__(self):
                        super().__init__()
                        self.load_gui_config()

                automator = SafeAutomator()

                try:
                    # 执行检测
                    results = automator.gui_detect_screen()
                    targets = automator.parse_detection_results(results)

                    if not targets:
                        print("⚠️ 未检测到目标，使用备用策略")
                        return False

                    # 选择目标
                    target = automator.select_target_by_confidence(targets, 'highest')

                    if target:
                        coords = automator.get_target_coordinates(target, 'center')

                        # 坐标验证
                        if automator.validate_coordinates(coords):
                            print("✅ 坐标验证通过")

                            # 安全执行操作
                            max_retries = 3
                            for attempt in range(max_retries):
                                try:
                                    success = automator.perform_mouse_click(coords, 'left', 'single', 0.3)
                                    if success:
                                        print(f"✅ 操作成功 (尝试 {attempt + 1}/{max_retries})")
                                        break
                                    else:
                                        print(f"⚠️ 操作失败，重试 {attempt + 1}/{max_retries}")
                                        time.sleep(1)
                                except Exception as e:
                                    print(f"❌ 操作异常: {e}")
                                    if attempt == max_retries - 1:
                                        print("❌ 达到最大重试次数，操作失败")
                                        return False
                                    time.sleep(1)
                        else:
                            print("❌ 坐标验证失败")
                            return False

                except Exception as e:
                    print(f"❌ 检测过程异常: {e}")
                    return False

                return True

            def main():
                """主函数 - 运行所有示例"""
                print("🎯 YOLO OpenCV检测器自动化操作完整示例")
                print("=" * 60)

                examples = [
                    ("办公软件自动化", office_automation_example),
                    ("多目标操作", multi_target_operation_example),
                    ("目标选择策略", target_selection_strategies_example),
                    ("错误处理机制", error_handling_example),
                ]

                results = []
                for name, example_func in examples:
                    try:
                        print(f"\n🚀 运行 {name} 示例...")
                        result = example_func()
                        results.append((name, result))
                        if result:
                            print(f"✅ {name} 示例完成")
                        else:
                            print(f"⚠️ {name} 示例部分完成")
                    except Exception as e:
                        print(f"❌ {name} 示例异常: {e}")
                        results.append((name, False))

                # 总结
                print("\n" + "=" * 60)
                print("📊 示例运行总结:")
                success_count = sum(1 for _, result in results if result)
                for name, result in results:
                    status = "✅ 成功" if result else "❌ 失败"
                    print(f"  {name}: {status}")

                print(f"\n📈 成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")

                print("\n💡 使用提示:")
                print("  • 确保屏幕上有可检测的界面元素")
                print("  • 检查YOLO模型配置是否正确")
                print("  • 验证系统权限设置")
                print("  • 根据实际需求调整参数")

            if __name__ == "__main__":
                main()

            
        except Exception as e:
            self.logger.error(f"检测程序执行失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print(f"🎯 {'2025.7.27'}")
    print(f"📝 {'基于YOLO的目标检测自动化脚本'}")
    print("=" * 60)
    
    try:
        detector = StandaloneDetector()
        detector.run_detection()
        
        print("\n✅ 程序执行完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 等待用户输入（避免窗口立即关闭）
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
