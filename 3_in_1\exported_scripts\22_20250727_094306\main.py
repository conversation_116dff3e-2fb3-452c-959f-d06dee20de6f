#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
22 - 独立运行脚本
基于YOLO的目标检测自动化脚本

自动生成时间: 2025-07-27 09:43:06
运行环境: Python 3.7+
依赖包: 请先运行 pip install -r requirements.txt

使用方法:
1. 确保已安装所有依赖包
2. 运行 python main.py 或双击 run.bat
3. 按照提示进行操作
"""

import sys
import os
import cv2
import numpy as np
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 导入工具模块
try:
    from utils.chinese_text_renderer import put_chinese_text, ChineseTextRenderer
    CHINESE_RENDERER_AVAILABLE = True
except ImportError:
    CHINESE_RENDERER_AVAILABLE = False
    print("⚠️ 中文渲染器不可用，中文字符可能显示为问号")

try:
    from utils.detection_utils import DetectionResult, BoundingBox
    DETECTION_UTILS_AVAILABLE = True
except ImportError:
    DETECTION_UTILS_AVAILABLE = False
    print("⚠️ 检测工具不可用，使用基础功能")

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('detection_log.txt', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class StandaloneDetector:
    """独立检测器"""
    
    def __init__(self):
        """初始化检测器"""
        self.logger = logger
        self.config = self._load_config()
        self.chinese_renderer = None
        
        if CHINESE_RENDERER_AVAILABLE:
            try:
                self.chinese_renderer = ChineseTextRenderer()
                self.logger.info("✅ 中文渲染器初始化成功")
            except Exception as e:
                self.logger.warning(f"中文渲染器初始化失败: {e}")
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = Path("config/settings.json")
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            self.logger.warning(f"加载配置失败: {e}")
        
        # 返回默认配置
        return {
            "template_name": "22",
            "description": "基于YOLO的目标检测自动化脚本",
            "confidence_threshold": 0.5,
            "nms_threshold": 0.4
        }
    
    def render_text_on_image(self, image: np.ndarray, text: str, 
                           position: Tuple[int, int], 
                           color: Tuple[int, int, int] = (0, 255, 0)) -> np.ndarray:
        """在图像上渲染文字"""
        if self.chinese_renderer and CHINESE_RENDERER_AVAILABLE:
            try:
                return put_chinese_text(image, text, position, 
                                      font_size=16, color=color, background=True)
            except Exception as e:
                self.logger.warning(f"中文渲染失败: {e}")
        
        # 回退到OpenCV渲染
        cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                   0.6, color, 2, cv2.LINE_AA)
        return image
    
    def run_detection(self):
        """运行检测主程序"""
        self.logger.info("🚀 开始运行检测程序...")
        self.logger.info(f"📋 模板: {self.config.get('template_name', 'Unknown')}")
        self.logger.info(f"📝 描述: {self.config.get('description', 'No description')}")
        
        try:
            # 这里插入用户的检测代码
                        #!/usr/bin/env python3
            # -*- coding: utf-8 -*-
            """
            GUI Detection Method Complete Copy
            This code 100% replicates GUI detection calls to ensure identical results
            """

            import sys
            import os
            import json
            from typing import List, Dict, Any, Optional
            from pathlib import Path

            # Robust project path detection for temporary file execution
            def setup_project_path():
                """Setup project path for module imports"""
                print(f"🔍 开始设置项目路径...")
                current_dir = Path(__file__).parent.absolute()
                print(f"📁 当前脚本目录: {current_dir}")

                # Method 1: 基于导出脚本的相对路径查找
                # 导出脚本位置: exported_scripts/22_20250727_094306/
                # 目标路径: ../../opencv_yolo/src/yolo_opencv_detector
                relative_paths = [
                    "../../opencv_yolo/src",  # 从导出脚本到主项目的相对路径
                    "../../../opencv_yolo/src",  # 备用路径1
                    "../../src",  # 备用路径2
                    "../src",  # 备用路径3
                ]

                for rel_path in relative_paths:
                    try:
                        src_path = (current_dir / rel_path).resolve()
                        yolo_path = src_path / "yolo_opencv_detector"

                        print(f"🔍 检查路径: {src_path}")
                        if yolo_path.exists() and yolo_path.is_dir():
                            print(f"✅ 找到yolo_opencv_detector模块: {yolo_path}")
                            if str(src_path) not in sys.path:
                                sys.path.insert(0, str(src_path))
                                print(f"✅ 已添加到sys.path: {src_path}")
                            return True
                        else:
                            print(f"❌ 路径不存在: {yolo_path}")
                    except Exception as e:
                        print(f"❌ 路径解析错误 {rel_path}: {e}")

                # Method 2: 绝对路径查找（基于当前项目结构）
                script_path = Path(__file__).parent.absolute()
                # 从脚本路径推断项目根目录
                possible_roots = []

                # 向上查找包含 "opencv_yolo" 的目录
                for parent in script_path.parents:
                    opencv_yolo_path = parent / "opencv_yolo"
                    if opencv_yolo_path.exists():
                        possible_roots.append(opencv_yolo_path)

                for root in possible_roots:
                    src_path = root / "src"
                    yolo_path = src_path / "yolo_opencv_detector"

                    print(f"🔍 检查绝对路径: {src_path}")
                    if yolo_path.exists() and yolo_path.is_dir():
                        print(f"✅ 找到yolo_opencv_detector模块: {yolo_path}")
                        if str(src_path) not in sys.path:
                            sys.path.insert(0, str(src_path))
                            print(f"✅ 已添加到sys.path: {src_path}")
                        return True

                # Method 3: 尝试常见的项目位置
                common_patterns = [
                    "*/opencv_yolo/src",
                    "*/3_in_1/opencv_yolo/src",
                    "*/Autowork*/3_in_1/opencv_yolo/src",
                ]

                for pattern in common_patterns:
                    try:
                        # 在用户文档目录中搜索
                        docs_path = Path.home() / "Documents"
                        if docs_path.exists():
                            for path in docs_path.rglob("yolo_opencv_detector"):
                                if path.is_dir() and path.parent.name == "src":
                                    src_path = path.parent
                                    print(f"🔍 在文档目录找到: {src_path}")
                                    if str(src_path) not in sys.path:
                                        sys.path.insert(0, str(src_path))
                                        print(f"✅ 已添加到sys.path: {src_path}")
                                    return True
                    except Exception as e:
                        print(f"❌ 搜索错误: {e}")

                # Method 4: 错误处理和调试信息
                print("❌ 无法找到yolo_opencv_detector模块")
                print(f"📁 当前脚本位置: {Path(__file__).absolute()}")
                print(f"📁 当前工作目录: {Path.cwd()}")
                print(f"📋 当前sys.path前5项:")
                for i, path in enumerate(sys.path[:5]):
                    print(f"  {i}: {path}")

                # 提供手动修复建议
                print("\n🔧 手动修复建议:")
                print("1. 确保从正确的目录运行脚本")
                print("2. 检查项目结构是否完整")
                print("3. 验证yolo_opencv_detector模块是否存在")

                return False

            # Setup project path
            setup_project_path()

            # Configuration parameters identical to GUI
            CONFIDENCE_THRESHOLD = 0.5  # GUI default confidence threshold
            NMS_THRESHOLD = 0.4         # GUI default NMS threshold

            class GUIDetectorCopy:
                """Class that completely replicates GUI detection methods"""

                def __init__(self):
                    self.yolo_detector = None
                    self.screen_capture = None
                    self.config_manager = None
                    self.smart_detection_manager = None
                    self.detection_count = 0
                    self.current_fps = 0.0

                    self._init_gui_services()
                    print("成功: GUI检测服务已初始化")

                def _init_gui_services(self):
                    """Initialize GUI services with enhanced error handling"""
                    import os
                    print("信息: 正在初始化GUI服务...")
                    print(f"信息: 当前工作目录: {os.getcwd()}")
                    print(f"信息: Python路径条目数: {len(sys.path)}")

                    # Debug: Show relevant sys.path entries
                    for i, path in enumerate(sys.path[:5]):
                        print(f"信息: sys.path[{i}]: {path}")

                    try:
                        print("信息: 尝试导入yolo_opencv_detector模块...")

                        # Import with detailed error reporting
                        try:
                            from yolo_opencv_detector.utils.config_manager import ConfigManager
                            print("成功: ConfigManager导入成功")
                        except ImportError as e:
                            print(f"错误: ConfigManager导入失败: {e}")
                            print("调试: 检查yolo_opencv_detector包是否可访问...")
                            try:
                                import yolo_opencv_detector
                                print(f"调试: yolo_opencv_detector包位置: {yolo_opencv_detector.__file__}")
                            except ImportError:
                                print("调试: 在sys.path中未找到yolo_opencv_detector包")
                            raise

                        try:
                            from yolo_opencv_detector.core.yolo_detector_v2 import YOLODetectorV2
                            print("成功: YOLODetectorV2导入成功")
                        except ImportError as e:
                            print(f"错误: YOLODetectorV2导入失败: {e}")
                            raise

                        try:
                            from yolo_opencv_detector.core.screen_capture_v2 import ScreenCaptureServiceV2
                            print("成功: ScreenCaptureServiceV2导入成功")
                        except ImportError as e:
                            print(f"错误: ScreenCaptureServiceV2导入失败: {e}")
                            raise

                        try:
                            from yolo_opencv_detector.core.smart_detection_manager import SmartDetectionManager
                            print("成功: SmartDetectionManager导入成功")
                        except ImportError as e:
                            print(f"错误: SmartDetectionManager导入失败: {e}")
                            raise

                        # Initialize services
                        print("信息: 所有导入成功，正在初始化服务...")

                        # 设置正确的模型路径
                        import os
                        script_dir = Path(__file__).parent.absolute()
                        models_dir = script_dir / "../../opencv_yolo/models"
                        models_dir = models_dir.resolve()

                        print(f"信息: 模型目录路径: {models_dir}")
                        if models_dir.exists():
                            print("✅ 模型目录存在")
                            # 设置环境变量，让配置管理器知道模型位置
                            os.environ['YOLO_MODELS_DIR'] = str(models_dir)
                        else:
                            print("❌ 模型目录不存在")

                        self.config_manager = ConfigManager()
                        print("成功: ConfigManager已初始化")

                        # 临时切换到项目目录以确保模型路径正确
                        original_cwd = os.getcwd()
                        project_dir = script_dir / "../../opencv_yolo"
                        project_dir = project_dir.resolve()

                        print(f"信息: 切换到项目目录: {project_dir}")
                        os.chdir(str(project_dir))

                        try:
                            self.yolo_detector = YOLODetectorV2(self.config_manager)
                            print("成功: YOLODetectorV2已初始化")
                        finally:
                            # 恢复原始工作目录
                            os.chdir(original_cwd)
                            print(f"信息: 恢复工作目录: {original_cwd}")

                        self.screen_capture = ScreenCaptureServiceV2()
                        print("成功: ScreenCaptureServiceV2已初始化")

                        self.smart_detection_manager = SmartDetectionManager()
                        print("成功: SmartDetectionManager已初始化")

                    except ImportError as e:
                        print(f"错误: GUI服务导入失败: {e}")
                        print("解决方案: 请确保从项目根目录运行")
                        print("解决方案: 项目结构应为: project_root/src/yolo_opencv_detector/")
                        print("当前: 请尝试从包含'src'文件夹的目录运行")
                        raise
                    except Exception as e:
                        print(f"错误: GUI服务初始化失败: {e}")
                        import traceback
                        traceback.print_exc()
                        raise

                def gui_detect_screen(self) -> List[Dict[str, Any]]:
                    """Complete copy of GUI screen detection method with 模式 detection"""
                    import os
                    print("信息: 开始屏幕检测（GUI方法复制）...")

                    # 确保在正确的工作目录中进行检测
                    original_cwd = os.getcwd()
                    script_dir = Path(__file__).parent.absolute()
                    project_dir = script_dir / "../../opencv_yolo"
                    project_dir = project_dir.resolve()

                    try:
                        # 切换到项目目录以确保模型路径正确
                        os.chdir(str(project_dir))
                        # Step 1: Screenshot (identical to GUI)
                        image = self.screen_capture.capture_fullscreen()
                        if image is None:
                            print("错误: 截图失败")
                            return []

                        print(f"成功: 截图已捕获: {image.shape}")

                        # Step 2: Detect current GUI detection 模式
                        detection_模式 = self._detect_gui_模式()
                        print(f"信息: 检测到GUI模式: {detection_模式}")

                        # Step 3: Configure smart detection manager based on GUI 模式
                        self._configure_detection_模式(detection_模式)

                        # Step 4: Execute detection (identical to GUI)
                        detections = []
                        if self.yolo_detector:
                            # Get current GUI parameters
                            gui_params = self._get_gui_detection_params()
                            confidence = gui_params.get('confidence_threshold', CONFIDENCE_THRESHOLD)
                            nms_threshold = gui_params.get('nms_threshold', NMS_THRESHOLD)

                            print(f"信息: 使用GUI参数 - 置信度: {confidence}, NMS: {nms_threshold}")

                            detections = self.yolo_detector.detect(
                                image,
                                confidence=confidence,
                                nms_threshold=nms_threshold
                            )

                            self.detection_count += 1
                            stats = self.yolo_detector.get_performance_stats()
                            self.current_fps = stats.get('fps', 0.0)

                            print(f"成功: YOLO检测完成: {len(detections)} 个目标")
                            print(f"统计: 性能: FPS={self.current_fps:.2f}")

                        # Step 5: Smart detection processing (identical to GUI)
                        if self.smart_detection_manager:
                            detection_result = self.smart_detection_manager.process_detections(image, detections)
                            actual_detections = detection_result.get("detections", [])
                            actual_模式 = detection_result.get("detection_模式", "yolo_detection")

                            print(f"成功: 智能检测处理完成: {len(actual_detections)} 个目标 ({actual_模式})")

                            return self._convert_detections(actual_detections, actual_模式)
                        else:
                            return self._convert_detections(detections, "yolo_detection")

                    except Exception as e:
                        print(f"错误: GUI检测方法执行失败: {e}")
                        import traceback
                        traceback.print_exc()
                        return []
                    finally:
                        # 恢复原始工作目录
                        os.chdir(original_cwd)

                def _detect_gui_模式(self) -> str:
                    """Detect current GUI detection 模式 by checking GUI state"""
                    try:
                        # Method 1: Try to read GUI current template selection from config
                        current_template = self._get_gui_current_template()
                        if current_template:
                            print(f"信息: GUI已选择模板: {current_template}")
                            return "template_matching"

                        # Method 2: Check if there are template files and try to detect recent selection
                        templates_dir = Path("templates")
                        if templates_dir.exists():
                            template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
                            if template_files:
                                print(f"信息: Found {len(template_files)} template files")

                                # Try to detect the most recently used template
                                recent_template = self._detect_recent_template_usage()
                                if recent_template:
                                    print(f"信息: Detected recent template usage: {recent_template}")
                                    return "template_matching"

                                # If templates exist but no recent usage, check for GUI state files
                                gui_state = self._check_gui_state_files()
                                if gui_state and gui_state.get('template_模式'):
                                    return "template_matching"

                        # Default to YOLO detection
                        print("信息: No template selection detected, using YOLO 模式")
                        return "yolo_detection"

                    except Exception as e:
                        print(f"警告: Failed to detect GUI 模式: {e}")
                        return "yolo_detection"

                def _configure_detection_模式(self, 模式: str):
                    """Configure detection 模式 to match GUI"""
                    try:
                        if 模式 == "template_matching":
                            # Step 1: Try to get GUI current selected template
                            gui_template_name = self._get_gui_current_template()
                            template_data = None

                            if gui_template_name:
                                print(f"信息: GUI selected template: {gui_template_name}")
                                # Try to load the specific template that GUI is using
                                template_data = self._load_specific_template(gui_template_name)

                            if not template_data:
                                print("警告: Could not load GUI selected template, trying available templates...")
                                # Fallback: load any available template
                                template_data = self._load_available_template()

                            if template_data:
                                self.smart_detection_manager.set_template_matching(True, template_data)
                                print(f"信息: 已启用模板匹配，使用模板: {template_data['name']}")
                                if gui_template_name:
                                    # Enhanced template matching verification
                                    is_match = self._verify_template_match(gui_template_name, template_data['name'])

                                    if is_match:
                                        print("✅ 成功: 使用与GUI相同的模板 (verified match)")
                                    else:
                                        print("⚠️ 警告: 使用与GUI不同的模板")
                                else:
                                    print("⚠️ 警告: 使用与GUI不同的模板")
                            else:
                                print("警告: Template matching 模式 detected but no valid template found")
                                print("信息: Falling back to YOLO detection")
                                self.smart_detection_manager.set_template_matching(False)
                        else:
                            # Use YOLO detection
                            self.smart_detection_manager.set_template_matching(False)
                            print("信息: Using YOLO detection 模式")

                    except Exception as e:
                        print(f"错误: Failed to configure detection 模式: {e}")
                        # Fallback to YOLO detection
                        self.smart_detection_manager.set_template_matching(False)

                def _load_specific_template(self, template_name: str) -> Optional[Dict[str, Any]]:
                    """Load a specific template by name with fuzzy matching"""
                    try:
                        templates_dir = Path("templates")
                        if not templates_dir.exists():
                            print(f"警告: 未找到模板目录")
                            return None

                        # Get all template files
                        template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg")) + list(templates_dir.glob("*.jpeg")) + list(templates_dir.glob("*.bmp"))

                        if not template_files:
                            print(f"警告: No template files found in {templates_dir}")
                            return None

                        # Method 1: Exact match
                        possible_extensions = ['.png', '.jpg', '.jpeg', '.bmp']
                        for ext in possible_extensions:
                            candidate = templates_dir / f"{template_name}{ext}"
                            if candidate.exists():
                                template_file = candidate
                                break
                        else:
                            template_file = None

                        # Method 2: Enhanced fuzzy matching if exact match fails
                        if not template_file:
                            print(f"信息: 精确匹配失败: '{template_name}', 尝试增强匹配...")

                            template_file = self._find_template_with_enhanced_matching(template_name, template_files)

                            if template_file:
                                print(f"信息: 找到增强匹配: {template_file.stem}")
                            else:
                                print(f"警告: 未找到增强匹配: {template_name}")

                        if not template_file:
                            print(f"警告: 未找到匹配的模板文件: {template_name}")
                            print(f"可用模板: {[f.stem for f in template_files]}")
                            return None

                        # Load template image
                        import cv2
                        template_image = cv2.imread(str(template_file))
                        if template_image is None:
                            print(f"错误: 无法加载模板图像: {template_file}")
                            return None

                        actual_name = template_file.stem
                        print(f"成功: 已加载指定模板: {actual_name} (requested: {template_name})")
                        return {
                            'name': actual_name,  # Use actual file name
                            'image': template_image,
                            'threshold': 0.8,  # Default threshold
                            'path': str(template_file)
                        }

                    except Exception as e:
                        print(f"错误: Failed to load specific template {template_name}: {e}")
                        return None

                def _load_available_template(self) -> Optional[Dict[str, Any]]:
                    """Load any available template for template matching (fallback)"""
                    try:
                        templates_dir = Path("templates")
                        if not templates_dir.exists():
                            return None

                        # Look for template files
                        template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
                        if not template_files:
                            return None

                        # Sort by modification time (most recent first) to get likely current template
                        template_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                        # Try to load templates in order of recency
                        for template_file in template_files:
                            try:
                                # Load template image
                                import cv2
                                template_image = cv2.imread(str(template_file))
                                if template_image is not None:
                                    print(f"成功: 已加载备用模板: {template_file.stem}")
                                    return {
                                        'name': template_file.stem,
                                        'image': template_image,
                                        'threshold': 0.8,  # Default threshold
                                        'path': str(template_file)
                                    }
                            except Exception as e:
                                print(f"警告: Could not load template {template_file}: {e}")
                                continue

                        print("错误: No valid template files found")
                        return None

                    except Exception as e:
                        print(f"错误: Failed to load available template: {e}")
                        return None

                def _get_gui_detection_params(self) -> Dict[str, Any]:
                    """Get current GUI detection parameters"""
                    # This is a simplified version - in real implementation,
                    # this would read from the actual GUI state
                    return {
                        'confidence_threshold': CONFIDENCE_THRESHOLD,
                        'nms_threshold': NMS_THRESHOLD,
                        'detection_interval': 1.0
                    }

                def _get_gui_current_template(self) -> Optional[str]:
                    """Get GUI current selected template name"""
                    try:
                        # Method 1: Try to read from config files
                        config_file = Path("configs/user_config.yaml")
                        if config_file.exists():
                            try:
                                import yaml
                                with open(config_file, 'r', encoding='utf-8') as f:
                                    config = yaml.safe_load(f)

                                # Check for current template in config
                                current_template = config.get('current_template')
                                if current_template:
                                    print(f"信息: 在配置中找到当前模板: {current_template}")
                                    return current_template
                            except Exception as e:
                                print(f"调试: Could not read config file: {e}")

                        # Method 2: Try to detect from recent log files
                        recent_template = self._parse_recent_logs_for_template()
                        if recent_template:
                            return recent_template

                        # Method 3: Try to detect from GUI state (if available)
                        gui_template = self._detect_gui_template_from_runtime()
                        if gui_template:
                            return gui_template

                        return None

                    except Exception as e:
                        print(f"警告: Failed to get GUI current template: {e}")
                        return None

                def _parse_recent_logs_for_template(self) -> Optional[str]:
                    """Parse recent log files to find current template selection"""
                    try:
                        # Look for recent log files
                        log_patterns = [
                            "logs/*.log",
                            "*.log",
                            "yolo_opencv_detector.log"
                        ]

                        recent_logs = []
                        for pattern in log_patterns:
                            log_files = list(Path(".").glob(pattern))
                            recent_logs.extend(log_files)

                        if not recent_logs:
                            return None

                        # Sort by modification time, get most recent
                        recent_logs.sort(key=lambda x: x.stat().st_mtime, reverse=True)

                        # Parse the most recent log file
                        for log_file in recent_logs[:3]:  # Check up to 3 most recent logs
                            try:
                                with open(log_file, 'r', encoding='utf-8') as f:
                                    lines = f.readlines()

                                # Look for template selection in recent lines (last 100 lines)
                                for line in reversed(lines[-100:]):
                                    if "选择模板:" in line:
                                        # Extract template name from log line
                                        template_name = line.split("选择模板:")[-1].strip()
                                        if template_name and template_name != "unknown":
                                            print(f"信息: 在日志中找到最近的模板选择: {template_name}")
                                            return template_name
                                    elif "template_selected" in line or "Template selected" in line:
                                        # Alternative log format
                                        parts = line.split()
                                        for i, part in enumerate(parts):
                                            if "template" in part.lower() and i + 1 < len(parts):
                                                template_name = parts[i + 1].strip()
                                                if template_name:
                                                    print(f"信息: Found template selection: {template_name}")
                                                    return template_name
                            except Exception as e:
                                print(f"调试: Could not parse log file {log_file}: {e}")
                                continue

                        return None

                    except Exception as e:
                        print(f"警告: Failed to parse recent logs: {e}")
                        return None

                def _detect_recent_template_usage(self) -> Optional[str]:
                    """Detect recent template usage from various sources"""
                    try:
                        # Check for recent template usage indicators
                        templates_dir = Path("templates")
                        if not templates_dir.exists():
                            return None

                        # Method 1: Check file access times
                        template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
                        if not template_files:
                            return None

                        # Sort by access time (most recently accessed first)
                        template_files.sort(key=lambda x: x.stat().st_atime, reverse=True)

                        # Get the most recently accessed template
                        recent_template = template_files[0]
                        template_name = recent_template.stem

                        # Check if it was accessed recently (within last hour)
                        import time
                        current_time = time.time()
                        access_time = recent_template.stat().st_atime

                        if current_time - access_time < 3600:  # Within last hour
                            print(f"信息: Recently accessed template: {template_name}")
                            return template_name

                        return None

                    except Exception as e:
                        print(f"警告: Failed to detect recent template usage: {e}")
                        return None

                def _check_gui_state_files(self) -> Dict[str, Any]:
                    """Check GUI state files for current configuration"""
                    try:
                        # Check for various GUI state files
                        state_files = [
                            "gui_state.json",
                            "configs/gui_state.json",
                            "temp/gui_state.json",
                            ".gui_state"
                        ]

                        for state_file in state_files:
                            state_path = Path(state_file)
                            if state_path.exists():
                                try:
                                    with open(state_path, 'r', encoding='utf-8') as f:
                                        state_data = json.load(f)

                                    if 'template_模式' in state_data or 'current_template' in state_data:
                                        print(f"信息: Found GUI state in {state_file}")
                                        return state_data
                                except Exception as e:
                                    print(f"调试: Could not read state file {state_file}: {e}")
                                    continue

                        return {}

                    except Exception as e:
                        print(f"警告: Failed to check GUI state files: {e}")
                        return {}

                def _detect_gui_template_from_runtime(self) -> Optional[str]:
                    """Try to detect GUI template from runtime environment"""
                    try:
                        # This is a placeholder for more advanced GUI state detection
                        # In a real implementation, this could:
                        # 1. Check shared memory
                        # 2. Read from GUI process communication
                        # 3. Check temporary state files
                        # 4. Use IPC mechanisms

                        # For now, return None as this requires more complex implementation
                        return None

                    except Exception as e:
                        print(f"警告: Failed to detect GUI template from runtime: {e}")
                        return None

                def _find_template_with_enhanced_matching(self, template_name: str, template_files: List[Path]) -> Optional[Path]:
                    """Enhanced template matching with multiple strategies"""
                    try:
                        template_name_str = str(template_name).strip()

                        # Strategy 1: Prefix matching (E -> E_*)
                        print(f"信息: 尝试前缀匹配: '{template_name_str}'...")
                        for candidate_file in template_files:
                            candidate_stem = candidate_file.stem
                            if candidate_stem.startswith(template_name_str + '_'):
                                print(f"成功: 找到前缀匹配: {candidate_stem}")
                                return candidate_file

                        # Strategy 2: Suffix matching (*_E)
                        print(f"信息: Trying suffix matching for '{template_name_str}'...")
                        for candidate_file in template_files:
                            candidate_stem = candidate_file.stem
                            if candidate_stem.endswith('_' + template_name_str):
                                print(f"成功: Found suffix match: {candidate_stem}")
                                return candidate_file

                        # Strategy 3: Contains matching (*E* or *_E_*)
                        print(f"信息: Trying contains matching for '{template_name_str}'...")
                        for candidate_file in template_files:
                            candidate_stem = candidate_file.stem
                            if ('_' + template_name_str + '_' in candidate_stem or
                                candidate_stem.startswith(template_name_str) or
                                candidate_stem.endswith(template_name_str)):
                                print(f"成功: Found contains match: {candidate_stem}")
                                return candidate_file

                        # Strategy 4: Case-insensitive matching
                        print(f"信息: Trying case-insensitive matching for '{template_name_str}'...")
                        template_lower = template_name_str.lower()
                        for candidate_file in template_files:
                            candidate_stem = candidate_file.stem.lower()
                            if (candidate_stem.startswith(template_lower + '_') or
                                candidate_stem.endswith('_' + template_lower) or
                                template_lower in candidate_stem):
                                print(f"成功: Found case-insensitive match: {candidate_file.stem}")
                                return candidate_file

                        # Strategy 5: Fuzzy similarity matching
                        print(f"信息: Trying fuzzy similarity matching for '{template_name_str}'...")
                        best_match = None
                        best_score = 0

                        for candidate_file in template_files:
                            candidate_stem = candidate_file.stem

                            # Calculate similarity scores
                            scores = []

                            # Score 1: Direct substring match
                            if template_name_str in candidate_stem:
                                scores.append(0.8)
                            elif template_name_str.lower() in candidate_stem.lower():
                                scores.append(0.7)

                            # Score 2: Prefix similarity
                            if candidate_stem.startswith(template_name_str):
                                scores.append(0.9)
                            elif candidate_stem.lower().startswith(template_name_str.lower()):
                                scores.append(0.8)

                            # Score 3: Length-based similarity
                            if len(template_name_str) > 0:
                                common_chars = sum(1 for c in template_name_str if c in candidate_stem)
                                length_score = common_chars / len(template_name_str)
                                scores.append(length_score * 0.6)

                            # Use best score for this candidate
                            if scores:
                                candidate_score = max(scores)
                                if candidate_score > best_score:
                                    best_score = candidate_score
                                    best_match = candidate_file

                        # Use best match if score is good enough
                        if best_match and best_score > 0.6:
                            print(f"成功: Found fuzzy match: {best_match.stem} (score: {best_score:.2f})")
                            return best_match

                        # Strategy 6: First character matching (as last resort)
                        if len(template_name_str) > 0:
                            print(f"信息: Trying first character matching for '{template_name_str[0]}'...")
                            first_char = template_name_str[0].lower()
                            for candidate_file in template_files:
                                if candidate_file.stem.lower().startswith(first_char):
                                    print(f"信息: Found first character match: {candidate_file.stem}")
                                    return candidate_file

                        print(f"警告: No match found for '{template_name_str}' using any strategy")
                        return None

                    except Exception as e:
                        print(f"错误: 增强模板匹配失败: {e}")
                        return None

                def _verify_template_match(self, gui_template_name: str, actual_template_name: str) -> bool:
                    """Verify if GUI template name matches actual template name"""
                    try:
                        gui_name = str(gui_template_name).strip()
                        actual_name = str(actual_template_name).strip()

                        # Exact match
                        if gui_name == actual_name:
                            return True

                        # Prefix match (E matches E_20250706_103005)
                        if actual_name.startswith(gui_name + '_'):
                            return True

                        # Suffix match (*_E matches E)
                        if actual_name.endswith('_' + gui_name):
                            return True

                        # Contains match (E_*_something matches E)
                        if gui_name in actual_name:
                            return True

                        # Case insensitive matching
                        if (gui_name.lower() == actual_name.lower() or
                            actual_name.lower().startswith(gui_name.lower() + '_') or
                            gui_name.lower() in actual_name.lower()):
                            return True

                        # Clean comparison (remove separators)
                        gui_clean = gui_name.replace('_', '').replace('-', '').replace(' ', '').lower()
                        actual_clean = actual_name.replace('_', '').replace('-', '').replace(' ', '').lower()

                        if gui_clean == actual_clean or gui_clean in actual_clean:
                            return True

                        return False

                    except Exception as e:
                        print(f"警告: 模板匹配验证失败: {e}")
                        return False

                def _convert_detections(self, detections: List[Any], detection_模式: str) -> List[Dict[str, Any]]:
                    """Convert detection results to standard format"""
                    results = []

                    for i, det in enumerate(detections):
                        try:
                            if hasattr(det, 'bbox'):
                                bbox = det.bbox
                                if isinstance(bbox, (list, tuple)) and len(bbox) >= 4:
                                    x, y, w, h = bbox[:4]

                                    result = {
                                        'id': i,
                                        'class_name': getattr(det, 'class_name', 'unknown'),
                                        'confidence': getattr(det, 'confidence', 0.0),
                                        'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                                        'center': {'x': int(x + w / 2), 'y': int(y + h / 2)},
                                        'area': int(w * h),
                                        'detection_模式': detection_模式
                                    }
                                    results.append(result)

                            elif isinstance(det, dict):
                                bbox = det.get('bbox', [0, 0, 0, 0])
                                if len(bbox) >= 4:
                                    x, y, w, h = bbox[:4]

                                    result = {
                                        'id': i,
                                        'class_name': det.get('class_name', 'unknown'),
                                        'confidence': det.get('confidence', 0.0),
                                        'bbox': {'x': int(x), 'y': int(y), 'width': int(w), 'height': int(h)},
                                        'center': {'x': int(x + w / 2), 'y': int(y + h / 2)},
                                        'area': int(w * h),
                                        'detection_模式': detection_模式
                                    }
                                    results.append(result)
                        except Exception as e:
                            print(f"警告: Failed to convert detection result {i}: {e}")
                            continue

                    return results

                def print_results(self, results: List[Dict[str, Any]]):
                    """Print detection results"""
                    if not results:
                        print("信息: 未检测到目标")
                        return

                    print(f"RESULTS: GUI detection results: {len(results)} targets")
                    print("=" * 70)

                    for i, result in enumerate(results, 1):
                        print(f"TARGET {i}:")
                        print(f"   Class: {result['class_name']}")
                        print(f"   Confidence: {result['confidence']:.3f}")
                        print(f"   中心: ({result['center']['x']}, {result['center']['y']})")
                        print(f"   BBox: x={result['bbox']['x']}, y={result['bbox']['y']}, w={result['bbox']['width']}, h={result['bbox']['height']}")
                        print(f"   Detection Mode: {result.get('detection_模式', 'unknown')}")
                        print()

                    print("=" * 70)
                    print(f"统计: Detection count={self.detection_count}, FPS={self.current_fps:.2f}")

                # ========================================
                # 🎯 目标操作和自动化办公功能模块
                # ========================================

                def parse_detection_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
                    """
                    解析检测结果，提取完整的目标信息

                    Args:
                        results: gui_detect_screen()返回的检测结果列表

                    Returns:
                        解析后的目标信息列表，包含完整的坐标和属性信息
                    """
                    parsed_targets = []

                    try:
                        for i, result in enumerate(results):
                            # 提取基础信息
                            bbox = result.get('bbox', {})
                            center = result.get('center', {})

                            # 计算详细坐标信息
                            x = bbox.get('x', 0)
                            y = bbox.get('y', 0)
                            width = bbox.get('width', 0)
                            height = bbox.get('height', 0)

                            # 计算边界框四个顶角坐标
                            top_left = {'x': x, 'y': y}
                            top_right = {'x': x + width, 'y': y}
                            bottom_left = {'x': x, 'y': y + height}
                            bottom_right = {'x': x + width, 'y': y + height}

                            # 计算中心点坐标
                            center_x = center.get('x', x + width // 2)
                            center_y = center.get('y', y + height // 2)

                            # 构建完整的目标信息
                            target = {
                                'id': i,
                                'class_name': result.get('class_name', 'unknown'),
                                'confidence': result.get('confidence', 0.0),
                                'detection_模式': result.get('detection_模式', 'unknown'),

                                # 边界框信息
                                'bbox': {
                                    'x': x, 'y': y, 'width': width, 'height': height,
                                    'area': result.get('area', width * height)
                                },

                                # 坐标信息
                                'coordinates': {
                                    'center': {'x': center_x, 'y': center_y},
                                    'top_left': top_left,
                                    'top_right': top_right,
                                    'bottom_left': bottom_left,
                                    'bottom_right': bottom_right,
                                    'top_center': {'x': center_x, 'y': y},
                                    'bottom_center': {'x': center_x, 'y': y + height},
                                    'left_center': {'x': x, 'y': center_y},
                                    'right_center': {'x': x + width, 'y': center_y}
                                },

                                # 尺寸信息
                                'dimensions': {
                                    'width': width,
                                    'height': height,
                                    'area': width * height,
                                    'perimeter': 2 * (width + height),
                                    'aspect_ratio': width / height if height > 0 else 0
                                },

                                # 原始数据
                                'raw_data': result
                            }

                            parsed_targets.append(target)

                    except Exception as e:
                        print(f"错误: 检测结果解析失败: {e}")
                        import traceback
                        traceback.print_exc()

                    return parsed_targets

                # ========================================
                # 🎯 目标选择策略方法
                # ========================================

                def select_target_by_confidence(self, targets: List[Dict[str, Any]], 模式: str = 'highest') -> Optional[Dict[str, Any]]:
                    """
                    按置信度选择目标

                    Args:
                        targets: 解析后的目标列表
                        模式: 'highest' 最高置信度, 'lowest' 最低置信度

                    Returns:
                        选中的目标，如果没有目标则返回None
                    """
                    if not targets:
                        return None

                    try:
                        if 模式 == 'highest':
                            return max(targets, key=lambda t: t.get('confidence', 0))
                        elif 模式 == 'lowest':
                            return min(targets, key=lambda t: t.get('confidence', 0))
                        else:
                            print(f"警告: 未知 confidence 模式: {模式}")
                            return targets[0]
                    except Exception as e:
                        print(f"错误: 按置信度选择目标失败: {e}")
                        return None

                def select_target_by_position(self, targets: List[Dict[str, Any]], position: str) -> Optional[Dict[str, Any]]:
                    """
                    按位置选择目标

                    Args:
                        targets: 解析后的目标列表
                        position: 'leftmost', 'rightmost', 'topmost', 'bottommost', 'center'

                    Returns:
                        选中的目标，如果没有目标则返回None
                    """
                    if not targets:
                        return None

                    try:
                        if position == 'leftmost':
                            return min(targets, key=lambda t: t['coordinates']['center']['x'])
                        elif position == 'rightmost':
                            return max(targets, key=lambda t: t['coordinates']['center']['x'])
                        elif position == 'topmost':
                            return min(targets, key=lambda t: t['coordinates']['center']['y'])
                        elif position == 'bottommost':
                            return max(targets, key=lambda t: t['coordinates']['center']['y'])
                        elif position == 'center':
                            # 选择最接近屏幕中心的目标
                            screen_center_x = 960  # 假设1920x1080屏幕
                            screen_center_y = 540
                            return min(targets, key=lambda t:
                                ((t['coordinates']['center']['x'] - screen_center_x) ** 2 +
                                 (t['coordinates']['center']['y'] - screen_center_y) ** 2) ** 0.5)
                        else:
                            print(f"警告: 未知 position 模式: {position}")
                            return targets[0]
                    except Exception as e:
                        print(f"错误: 按位置选择目标失败: {e}")
                        return None

                def select_target_by_size(self, targets: List[Dict[str, Any]], 模式: str = 'largest') -> Optional[Dict[str, Any]]:
                    """
                    按大小选择目标

                    Args:
                        targets: 解析后的目标列表
                        模式: 'largest' 最大面积, 'smallest' 最小面积

                    Returns:
                        选中的目标，如果没有目标则返回None
                    """
                    if not targets:
                        return None

                    try:
                        if 模式 == 'largest':
                            return max(targets, key=lambda t: t['dimensions']['area'])
                        elif 模式 == 'smallest':
                            return min(targets, key=lambda t: t['dimensions']['area'])
                        else:
                            print(f"警告: 未知 size 模式: {模式}")
                            return targets[0]
                    except Exception as e:
                        print(f"错误: 按大小选择目标失败: {e}")
                        return None

                def select_target_by_class(self, targets: List[Dict[str, Any]], class_name: str) -> List[Dict[str, Any]]:
                    """
                    按类别选择目标

                    Args:
                        targets: 解析后的目标列表
                        class_name: 目标类别名称

                    Returns:
                        匹配的目标列表
                    """
                    if not targets:
                        return []

                    try:
                        return [t for t in targets if t.get('class_name', '').lower() == class_name.lower()]
                    except Exception as e:
                        print(f"错误: 按类别选择目标失败: {e}")
                        return []

                def select_target_by_custom_condition(self, targets: List[Dict[str, Any]],
                                                    condition_func) -> List[Dict[str, Any]]:
                    """
                    按自定义条件选择目标

                    Args:
                        targets: 解析后的目标列表
                        condition_func: 自定义条件函数，接受目标字典作为参数，返回布尔值

                    Returns:
                        匹配的目标列表
                    """
                    if not targets:
                        return []

                    try:
                        return [t for t in targets if condition_func(t)]
                    except Exception as e:
                        print(f"错误: 按自定义条件选择目标失败: {e}")
                        return []

                # ========================================
                # 📍 坐标操作功能
                # ========================================

                def get_target_coordinates(self, target: Dict[str, Any], point_type: str = 'center') -> Optional[Dict[str, int]]:
                    """
                    获取目标的指定坐标点

                    Args:
                        target: 目标信息字典
                        point_type: 坐标点类型 ('center', 'top_left', 'top_right', 'bottom_left',
                                   'bottom_right', 'top_center', 'bottom_center', 'left_center', 'right_center')

                    Returns:
                        坐标字典 {'x': int, 'y': int}，失败时返回None
                    """
                    if not target or 'coordinates' not in target:
                        return None

                    try:
                        coordinates = target['coordinates']
                        if point_type in coordinates:
                            return coordinates[point_type]
                        else:
                            print(f"警告: 未知 点类型: {point_type}")
                            return coordinates.get('center')
                    except Exception as e:
                        print(f"错误: 获取目标坐标失败: {e}")
                        return None

                def get_bounding_box_corners(self, target: Dict[str, Any]) -> Optional[Dict[str, Dict[str, int]]]:
                    """
                    获取边界框四个顶角坐标

                    Args:
                        target: 目标信息字典

                    Returns:
                        包含四个顶角坐标的字典
                    """
                    if not target or 'coordinates' not in target:
                        return None

                    try:
                        coords = target['coordinates']
                        return {
                            'top_left': coords['top_left'],
                            'top_right': coords['top_right'],
                            'bottom_left': coords['bottom_left'],
                            'bottom_right': coords['bottom_right']
                        }
                    except Exception as e:
                        print(f"错误: 获取边界框顶角失败: {e}")
                        return None

                def calculate_target_area_and_dimensions(self, target: Dict[str, Any]) -> Optional[Dict[str, Any]]:
                    """
                    计算目标区域面积和尺寸信息

                    Args:
                        target: 目标信息字典

                    Returns:
                        尺寸信息字典
                    """
                    if not target or 'dimensions' not in target:
                        return None

                    try:
                        return target['dimensions']
                    except Exception as e:
                        print(f"错误: 计算目标尺寸失败: {e}")
                        return None

                def convert_to_relative_coordinates(self, absolute_coords: Dict[str, int],
                                                  screen_width: int = 1920, screen_height: int = 1080) -> Dict[str, float]:
                    """
                    将绝对坐标转换为相对坐标（0-1范围）

                    Args:
                        absolute_coords: 绝对坐标 {'x': int, 'y': int}
                        screen_width: 屏幕宽度
                        screen_height: 屏幕高度

                    Returns:
                        相对坐标 {'x': float, 'y': float}
                    """
                    try:
                        return {
                            'x': absolute_coords['x'] / screen_width,
                            'y': absolute_coords['y'] / screen_height
                        }
                    except Exception as e:
                        print(f"错误: 转换为相对坐标失败: {e}")
                        return {'x': 0.0, 'y': 0.0}

                def convert_to_absolute_coordinates(self, relative_coords: Dict[str, float],
                                                  screen_width: int = 1920, screen_height: int = 1080) -> Dict[str, int]:
                    """
                    将相对坐标转换为绝对坐标

                    Args:
                        relative_coords: 相对坐标 {'x': float, 'y': float}
                        screen_width: 屏幕宽度
                        screen_height: 屏幕高度

                    Returns:
                        绝对坐标 {'x': int, 'y': int}
                    """
                    try:
                        return {
                            'x': int(relative_coords['x'] * screen_width),
                            'y': int(relative_coords['y'] * screen_height)
                        }
                    except Exception as e:
                        print(f"错误: 转换为绝对坐标失败: {e}")
                        return {'x': 0, 'y': 0}

                # ========================================
                # 🤖 自动化办公操作功能
                # ========================================

                def perform_mouse_click(self, coordinates: Dict[str, int], button: str = 'left',
                                       click_type: str = 'single', delay: float = 0.1) -> bool:
                    """
                    执行鼠标点击操作

                    Args:
                        coordinates: 点击坐标 {'x': int, 'y': int}
                        button: 鼠标按键 ('left', 'right', 'middle')
                        click_type: 点击类型 ('single', 'double')
                        delay: 点击后延时（秒）

                    Returns:
                        操作是否成功
                    """
                    try:
                        import pyautogui

                        # 安全检查：确保坐标在屏幕范围内
                        screen_width, screen_height = pyautogui.size()
                        x = max(0, min(coordinates['x'], screen_width - 1))
                        y = max(0, min(coordinates['y'], screen_height - 1))

                        print(f"信息: 执行 {click_type} {button} 点击位置 ({x}, {y})")

                        # 移动到目标位置
                        pyautogui.moveTo(x, y, duration=0.2)

                        # 执行点击
                        if click_type == 'single':
                            pyautogui.click(x, y, button=button)
                        elif click_type == 'double':
                            pyautogui.doubleClick(x, y, button=button)

                        # 延时
                        if delay > 0:
                            import time
                            time.sleep(delay)

                        print(f"成功: 鼠标点击完成")
                        return True

                    except Exception as e:
                        print(f"错误: 鼠标点击执行失败: {e}")
                        return False

                def perform_mouse_drag(self, start_coords: Dict[str, int], end_coords: Dict[str, int],
                                      duration: float = 1.0, button: str = 'left') -> bool:
                    """
                    执行鼠标拖拽操作

                    Args:
                        start_coords: 起始坐标 {'x': int, 'y': int}
                        end_coords: 结束坐标 {'x': int, 'y': int}
                        duration: 拖拽持续时间（秒）
                        button: 鼠标按键

                    Returns:
                        操作是否成功
                    """
                    try:
                        import pyautogui

                        start_x = start_coords['x']
                        start_y = start_coords['y']
                        end_x = end_coords['x']
                        end_y = end_coords['y']

                        print(f"信息: 执行 拖拽从 ({start_x}, {start_y}) to ({end_x}, {end_y})")

                        # 执行拖拽
                        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration, button=button)

                        print(f"成功: 鼠标拖拽完成")
                        return True

                    except Exception as e:
                        print(f"错误: 鼠标拖拽执行失败: {e}")
                        return False

                def perform_keyboard_input(self, text: str, typing_speed: float = 0.05) -> bool:
                    """
                    执行键盘文本输入

                    Args:
                        text: 要输入的文本
                        typing_speed: 打字速度（每个字符间隔秒数）

                    Returns:
                        操作是否成功
                    """
                    try:
                        import pyautogui

                        print(f"信息: 输入文本: '{text}'")

                        # 输入文本
                        pyautogui.typewrite(text, interval=typing_speed)

                        print(f"成功: 文本输入完成")
                        return True

                    except Exception as e:
                        print(f"错误: 键盘输入执行失败: {e}")
                        return False

                def perform_keyboard_shortcut(self, keys: List[str], delay: float = 0.1) -> bool:
                    """
                    执行键盘快捷键

                    Args:
                        keys: 按键列表，如 ['ctrl', 'c'] 表示 Ctrl+C
                        delay: 操作后延时（秒）

                    Returns:
                        操作是否成功
                    """
                    try:
                        import pyautogui

                        print(f"信息: 执行 keyboard shortcut: {'+'.join(keys)}")

                        # 执行快捷键
                        pyautogui.hotkey(*keys)

                        # 延时
                        if delay > 0:
                            import time
                            time.sleep(delay)

                        print(f"成功: 键盘快捷键完成")
                        return True

                    except Exception as e:
                        print(f"错误: 键盘快捷键执行失败: {e}")
                        return False

                def wait_and_delay(self, seconds: float) -> None:
                    """
                    等待指定时间

                    Args:
                        seconds: 等待时间（秒）
                    """
                    try:
                        import time
                        print(f"信息: 等待 {seconds} 秒...")
                        time.sleep(seconds)
                        print(f"成功: 等待完成")
                    except Exception as e:
                        print(f"错误: 等待失败: {e}")

                def perform_automation_sequence(self, 个操作: List[Dict[str, Any]]) -> bool:
                    """
                    执行自动化操作序列

                    Args:
                        个操作: 操作序列列表，每个操作包含类型和参数

                    Returns:
                        所有操作是否成功
                    """
                    try:
                        print(f"信息: 开始自动化序列，包含 {len(个操作)} 个操作")

                        for i, action in enumerate(个操作):
                            action_type = action.get('type', '')
                            params = action.get('params', {})

                            print(f"信息: 执行操作 {i+1}/{len(个操作)}: {action_type}")

                            success = False

                            if action_type == 'click':
                                success = self.perform_mouse_click(
                                    params.get('coordinates', {'x': 0, 'y': 0}),
                                    params.get('button', 'left'),
                                    params.get('click_type', 'single'),
                                    params.get('delay', 0.1)
                                )
                            elif action_type == 'drag':
                                success = self.perform_mouse_drag(
                                    params.get('start_coords', {'x': 0, 'y': 0}),
                                    params.get('end_coords', {'x': 0, 'y': 0}),
                                    params.get('duration', 1.0),
                                    params.get('button', 'left')
                                )
                            elif action_type == 'type':
                                success = self.perform_keyboard_input(
                                    params.get('text', ''),
                                    params.get('typing_speed', 0.05)
                                )
                            elif action_type == 'shortcut':
                                success = self.perform_keyboard_shortcut(
                                    params.get('keys', []),
                                    params.get('delay', 0.1)
                                )
                            elif action_type == 'wait':
                                self.wait_and_delay(params.get('seconds', 1.0))
                                success = True
                            else:
                                print(f"警告: 未知 action type: {action_type}")
                                continue

                            if not success:
                                print(f"错误: 操作 {i+1} 失败，停止序列")
                                return False

                        print(f"成功: 自动化序列成功完成")
                        return True

                    except Exception as e:
                        print(f"错误: 自动化序列执行失败: {e}")
                        return False

                # ========================================
                # 🛡️ 安全机制和操作日志
                # ========================================

                def validate_coordinates(self, coordinates: Dict[str, int]) -> bool:
                    """
                    验证坐标是否在安全范围内

                    Args:
                        coordinates: 坐标字典 {'x': int, 'y': int}

                    Returns:
                        坐标是否有效
                    """
                    try:
                        import pyautogui
                        screen_width, screen_height = pyautogui.size()

                        x = coordinates.get('x', -1)
                        y = coordinates.get('y', -1)

                        if x < 0 or x >= screen_width or y < 0 or y >= screen_height:
                            print(f"警告: 坐标 ({x}, {y}) 超出屏幕边界 ({screen_width}x{screen_height})")
                            return False

                        return True

                    except Exception as e:
                        print(f"错误: 坐标验证失败: {e}")
                        return False

                def log_automation_action(self, action_type: str, params: Dict[str, Any], success: bool) -> None:
                    """
                    记录自动化操作日志

                    Args:
                        action_type: 操作类型
                        params: 操作参数
                        success: 操作是否成功
                    """
                    try:
                        import time
                        timestamp = time.strftime('%Y-%m-%d %H:%M:%S')
                        status = "SUCCESS" if success else "FAILED"

                        log_entry = f"[{timestamp}] {status}: {action_type} - {params}"
                        print(f"LOG: {log_entry}")

                        # 可以扩展为写入日志文件
                        # with open('automation_log.txt', 'a', encoding='utf-8') as f:
                        #     f.write(log_entry + '\n')

                    except Exception as e:
                        print(f"错误: 自动化操作日志记录失败: {e}")

                # ========================================
                # 📚 完整使用示例和最佳实践
                # ========================================
                # 注意：详细的使用示例已移至GUI主界面的"📚 使用示例"标签页
                # 这里保留基础的示例方法供程序内部调用

                def automation_example_office_workflow(self) -> bool:
                    """
                    办公自动化工作流程基础示例

                    注意：详细的自动化操作示例已移至GUI主界面的"📚 使用示例"标签页
                    这里仅提供基础的检测和目标选择功能演示

                    Returns:
                        基础检测是否成功完成
                    """
                    try:
                        print("信息: 执行基础检测和目标选择演示...")

                        # 步骤1: 执行屏幕检测
                        print("🔍 执行屏幕检测...")
                        results = self.gui_detect_screen()

                        if not results:
                            print("⚠️ 未检测到目标")
                            print("💡 建议：")
                            print("   • 确保屏幕上有可见的界面元素")
                            print("   • 检查YOLO模型是否正确加载")
                            print("   • 尝试调整检测参数")
                            return False

                        # 步骤2: 解析检测结果
                        print("📋 解析检测结果...")
                        targets = self.parse_detection_results(results)
                        print(f"✅ 找到 {len(targets)} 个目标")

                        # 步骤3: 基础目标选择演示
                        print("🎯 目标选择演示:")

                        # 按置信度选择
                        highest_conf_target = self.select_target_by_confidence(targets, 'highest')
                        if highest_conf_target:
                            print(f"   最高置信度目标: {highest_conf_target['class_name']} ({highest_conf_target['confidence']:.3f})")
                            coords = self.get_target_coordinates(highest_conf_target, 'center')
                            print(f"   中心坐标: ({coords['x']}, {coords['y']})")

                        # 按位置选择
                        leftmost_target = self.select_target_by_position(targets, 'leftmost')
                        if leftmost_target:
                            print(f"   最左侧目标: {leftmost_target['class_name']}")

                        # 按大小选择
                        largest_target = self.select_target_by_size(targets, 'largest')
                        if largest_target:
                            dimensions = self.calculate_target_area_and_dimensions(largest_target)
                            print(f"   最大目标: {largest_target['class_name']} (面积: {dimensions['area']} 像素)")

                        print("\n📚 查看完整使用示例:")
                        print("   请在GUI主界面右侧面板中点击 '📚 使用示例' 标签页")
                        print("   那里包含详细的自动化操作代码示例和最佳实践")
                        print("   包括：")
                        print("   • 🏢 办公软件自动化")
                        print("   • 🎯 多目标操作")
                        print("   • 🔍 目标选择策略")
                        print("   • ⚡ 复杂工作流程")
                        print("   • 🛡️ 错误处理机制")

                        return True

                    except Exception as e:
                        print(f"❌ 基础检测演示失败: {e}")
                        print("💡 建议查看GUI主界面的 '📚 使用示例' 标签页获取详细帮助")
                        return False

                def automation_example_multi_target_operation(self) -> bool:
                    """
                    多目标操作基础示例

                    注意：详细的多目标操作示例已移至GUI主界面的"📚 使用示例"标签页
                    这里仅提供基础的多目标检测和选择功能演示

                    Returns:
                        基础多目标操作是否成功
                    """
                    try:
                        print("信息: 执行基础多目标检测和选择演示...")

                        # 执行检测
                        print("🔍 执行屏幕检测...")
                        results = self.gui_detect_screen()
                        targets = self.parse_detection_results(results)

                        if len(targets) < 2:
                            print("⚠️ 需要至少 2 个目标进行多目标操作演示")
                            print("💡 建议：")
                            print("   • 确保屏幕上有多个可检测的界面元素")
                            print("   • 尝试打开更多窗口或应用")
                            return False

                        print(f"✅ 找到 {len(targets)} 个目标，可进行多目标操作")

                        # 基础多目标选择演示
                        print("🎯 多目标选择演示:")

                        # 按位置选择
                        leftmost_target = self.select_target_by_position(targets, 'leftmost')
                        rightmost_target = self.select_target_by_position(targets, 'rightmost')
                        topmost_target = self.select_target_by_position(targets, 'topmost')
                        bottommost_target = self.select_target_by_position(targets, 'bottommost')

                        position_targets = [
                            (leftmost_target, "最左侧"),
                            (rightmost_target, "最右侧"),
                            (topmost_target, "最上方"),
                            (bottommost_target, "最下方")
                        ]

                        for target, description in position_targets:
                            if target:
                                coords = self.get_target_coordinates(target, 'center')
                                print(f"   {description}目标: {target['class_name']} 位置: ({coords['x']}, {coords['y']})")

                        # 按大小选择
                        largest_target = self.select_target_by_size(targets, 'largest')
                        smallest_target = self.select_target_by_size(targets, 'smallest')

                        if largest_target:
                            dimensions = self.calculate_target_area_and_dimensions(largest_target)
                            print(f"   最大目标: {largest_target['class_name']} 面积: {dimensions['area']} 像素")

                        if smallest_target:
                            dimensions = self.calculate_target_area_and_dimensions(smallest_target)
                            print(f"   最小目标: {smallest_target['class_name']} 面积: {dimensions['area']} 像素")

                        # 按置信度选择
                        highest_conf_target = self.select_target_by_confidence(targets, 'highest')
                        lowest_conf_target = self.select_target_by_confidence(targets, 'lowest')

                        if highest_conf_target:
                            print(f"   最高置信度: {highest_conf_target['class_name']} ({highest_conf_target['confidence']:.3f})")

                        if lowest_conf_target:
                            print(f"   最低置信度: {lowest_conf_target['class_name']} ({lowest_conf_target['confidence']:.3f})")

                        print("\n📚 查看完整多目标操作示例:")
                        print("   请在GUI主界面右侧面板中点击 '📚 使用示例' 标签页")
                        print("   选择 '🎯 多目标操作' 子标签页查看详细示例")
                        print("   包括：")
                        print("   • 多目标拖拽操作")
                        print("   • 复杂序列操作")
                        print("   • 目标间距离分析")
                        print("   • 批量处理操作")

                        return True

                    except Exception as e:
                        print(f"❌ 基础多目标操作演示失败: {e}")
                        print("💡 建议查看GUI主界面的 '📚 使用示例' 标签页获取详细帮助")
                        return False

            # Main function with automation examples
            if __name__ == "__main__":
                print("🎯 YOLO GUI检测与办公自动化系统")
                print("=" * 80)
                print("完整的办公工作流程检测和自动化解决方案")
                print("=" * 80)

                try:
                    detector = GUIDetectorCopy()

                    print("\n📊 Step 1: 执行GUI检测工作流程...")
                    results = detector.gui_detect_screen()

                    print("\n📋 Step 2: 显示检测结果...")
                    detector.print_results(results)

                    if results:
                        print("\n🎯 Step 3: 演示自动化功能...")

                        # 解析检测结果
                        targets = detector.parse_detection_results(results)
                        print(f"信息: 已解析 {len(targets)} 个目标用于自动化")

                        if targets:
                            # 展示目标选择功能
                            print("\n🔍 目标选择示例:")

                            highest_conf = detector.select_target_by_confidence(targets, 'highest')
                            if highest_conf:
                                print(f"  - 最高置信度: {highest_conf['class_name']} ({highest_conf['confidence']:.3f})")

                            leftmost = detector.select_target_by_position(targets, 'leftmost')
                            if leftmost:
                                coords = detector.get_target_coordinates(leftmost, 'center')
                                print(f"  - 最左侧目标: {leftmost['class_name']} at ({coords['x']}, {coords['y']})")

                            largest = detector.select_target_by_size(targets, 'largest')
                            if largest:
                                dims = detector.calculate_target_area_and_dimensions(largest)
                                print(f"  - 最大目标: {largest['class_name']} (area: {dims['area']})")

                            print("\n📍 坐标信息:")
                            for i, target in enumerate(targets[:3]):  # Show first 3 targets
                                corners = detector.get_bounding_box_corners(target)
                                if corners:
                                    print(f"  Target {i+1} ({target['class_name']}):")
                                    print(f"    - 中心: {detector.get_target_coordinates(target, 'center')}")
                                    print(f"    - 左上角: {corners['top_left']}")
                                    print(f"    - 右下角: {corners['bottom_right']}")

                            print("\n🤖 可用的自动化功能:")
                            print("  ✅ 鼠标点击（左键/右键/双击）")
                            print("  ✅ 目标间鼠标拖拽")
                            print("  ✅ 键盘输入和快捷键")
                            print("  ✅ 多步骤自动化序列")
                            print("  ✅ 坐标验证和安全检查")
                            print("  ✅ 操作日志和错误处理")

                            print("\n💡 详细使用示例请查看:")
                            print("  📚 GUI主界面 → 右侧面板 → '📚 使用示例' 标签页")
                            print("  包含完整的代码示例和最佳实践指导")

                            # 注意：详细示例已移至GUI的使用示例标签页

                        print("\n✅ 成功: GUI检测和自动化系统就绪!")
                        print("信息: 所有功能正常工作")
                        print("信息: 准备进行办公自动化任务")

                    else:
                        print("\n📝 信息: 未检测到目标")
                        print("建议:")
                        print("  1. 检查屏幕内容 - ensure there are visible UI elements")
                        print("  2. 降低置信度阈值 if needed")
                        print("  3. Verify YOLO 模式l is properly loaded")
                        print("  4. Try template matching 模式 for specific UI elements")

                except Exception as e:
                    print(f"\n❌ 错误: 系统初始化失败: {e}")
                    print("\n故障排除:")
                    print("  1. 确保已安装所有依赖项（pyautogui, opencv-python）")
                    print("  2. Check that YOLO 模式l files are present")
                    print("  3. 验证屏幕捕获权限")
                    import traceback
                    traceback.print_exc()

            
        except Exception as e:
            self.logger.error(f"检测程序执行失败: {e}")
            import traceback
            traceback.print_exc()

def main():
    """主函数"""
    print("=" * 60)
    print(f"🎯 {'22'}")
    print(f"📝 {'基于YOLO的目标检测自动化脚本'}")
    print("=" * 60)
    
    try:
        detector = StandaloneDetector()
        detector.run_detection()
        
        print("\n✅ 程序执行完成")
        
    except KeyboardInterrupt:
        print("\n⚠️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 等待用户输入（避免窗口立即关闭）
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
