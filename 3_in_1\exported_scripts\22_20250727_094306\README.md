# 22

基于YOLO的目标检测自动化脚本

## 📋 项目信息

- **项目名称**: 22
- **生成时间**: 2025-07-27 09:43:06
- **导出目录**: 22_20250727_094306
- **Python版本**: 3.7+

## 🚀 快速开始

### 方法1：使用批处理文件（推荐）
1. 双击 `run.bat` 文件
2. 脚本会自动检查并安装依赖
3. 按照提示进行操作

### 方法2：手动运行
1. 安装依赖包：
   ```bash
   pip install -r requirements.txt
   ```

2. 运行主脚本：
   ```bash
   python main.py
   ```

## 📁 文件结构

```
22_20250727_094306/
├── main.py                 # 主要检测代码
├── requirements.txt        # 依赖列表
├── run.bat                # Windows运行脚本
├── README.md              # 使用说明（本文件）
├── config/                # 配置文件目录
│   └── settings.json      # 检测参数配置
└── utils/                 # 工具模块
    ├── __init__.py
    ├── chinese_text_renderer.py  # 中文字符渲染
    └── detection_utils.py        # 检测工具函数
```

## ⚙️ 配置说明

### 检测参数配置 (config/settings.json)

```json
{
  "detection_settings": {
    "confidence_threshold": 0.5,    // 置信度阈值
    "nms_threshold": 0.4,          // NMS阈值
    "input_size": [640, 640],      // 输入图像尺寸
    "max_detections": 100          // 最大检测数量
  },
  "display_settings": {
    "show_confidence": true,       // 显示置信度
    "show_labels": true,          // 显示标签
    "font_size": 16,              // 字体大小
    "line_thickness": 2           // 线条粗细
  }
}
```

## 🔧 依赖包说明

### 核心依赖
- **opencv-python**: 图像处理和计算机视觉
- **numpy**: 数值计算
- **Pillow**: 图像处理
- **ultralytics**: YOLO模型支持

### 可选依赖
- **PyQt6**: GUI界面（如果需要）
- **matplotlib**: 图像显示和绘图
- **scipy**: 科学计算

## 🎯 功能特性

### ✅ 已实现功能
- 🔍 YOLO目标检测
- 🖼️ 图像处理和显示
- 📝 中文字符渲染支持
- 📊 检测结果可视化
- 📋 详细日志记录
- ⚙️ 配置文件支持

### 🔄 自定义配置
1. **修改检测参数**: 编辑 `config/settings.json`
2. **调整显示效果**: 修改 `display_settings` 部分
3. **更改输出设置**: 配置 `output_settings` 部分

## 🐛 故障排除

### 常见问题

#### 1. 依赖安装失败
**问题**: `pip install` 命令执行失败
**解决方案**:
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者升级pip
python -m pip install --upgrade pip
```

#### 2. OpenCV导入错误
**问题**: `ImportError: No module named 'cv2'`
**解决方案**:
```bash
pip uninstall opencv-python
pip install opencv-python
```

#### 3. 中文字符显示问题
**问题**: 中文字符显示为问号
**解决方案**:
- 确保系统已安装中文字体
- Windows系统通常自带，Linux可能需要安装：
  ```bash
  sudo apt-get install fonts-wqy-microhei
  ```

#### 4. 权限错误
**问题**: 文件读写权限不足
**解决方案**:
- 确保脚本有读写当前目录的权限
- Windows: 右键以管理员身份运行
- Linux/Mac: 使用 `chmod +x` 添加执行权限

#### 5. Python版本不兼容
**问题**: 语法错误或模块不兼容
**解决方案**:
- 确保使用Python 3.7或更高版本
- 检查Python版本：`python --version`

### 性能优化建议

1. **GPU加速**: 如果有NVIDIA GPU，安装CUDA版本的依赖
2. **内存优化**: 处理大图像时适当调整输入尺寸
3. **批处理**: 处理多个图像时使用批处理模式

## 📞 技术支持

如果遇到问题，请检查：
1. Python版本是否为3.7+
2. 所有依赖包是否正确安装
3. 配置文件格式是否正确
4. 输入数据是否符合要求

## 📄 许可证

本脚本基于原YOLO项目生成，请遵循相应的开源许可证。

## 🔄 更新日志

- **v1.0.0** (2025-07-27): 初始版本
  - 基础检测功能
  - 中文字符支持
  - 配置文件支持
  - 完整文档

---

*自动生成于 2025-07-27 09:43:06*
