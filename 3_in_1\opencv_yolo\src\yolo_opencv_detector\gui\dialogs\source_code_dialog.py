#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
源代码对话框 - 修复版本
显示与GUI完全相同的检测源代码
"""

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QTabWidget, QWidget,
    QTextEdit, QPushButton, QLabel, QApplication, QMessageBox,
    QProgressDialog, QInputDialog, QFileDialog
)
from PyQt6.QtCore import QThread, pyqtSignal, Qt
from PyQt6.QtGui import QFont, QIcon
from pathlib import Path
import sys
import subprocess
import tempfile
import os
import time


class CodeExecutionThread(QThread):
    """Code execution thread with proper UTF-8 encoding support"""
    output_signal = pyqtSignal(str)
    finished_signal = pyqtSignal(int)

    def __init__(self, code: str):
        super().__init__()
        self.code = code

    def run(self):
        """Execute code with proper UTF-8 encoding handling"""
        temp_file = None
        try:
            # Create temporary file with UTF-8 encoding
            with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
                f.write(self.code)
                temp_file = f.name

            # Execute the file with proper encoding and virtual environment
            import os
            from pathlib import Path

            # 确定正确的Python解释器路径和项目根目录
            current_python = sys.executable

            # 从当前文件路径推导项目根目录
            # 当前文件: .../3_in_1/opencv_yolo/src/yolo_opencv_detector/gui/dialogs/source_code_dialog.py
            # 需要向上级到达项目根目录
            current_file = Path(__file__).resolve()
            project_root = current_file.parent.parent.parent.parent.parent.parent

            print(f"DEBUG - 当前文件: {current_file}")
            print(f"DEBUG - 计算的项目根目录: {project_root}")
            print(f"DEBUG - 项目根目录是否存在: {project_root.exists()}")

            # 验证项目根目录是否正确(应该包含shared_env目录)
            shared_env_dir = project_root / "shared_env"
            print(f"DEBUG - shared_env目录: {shared_env_dir}")
            print(f"DEBUG - shared_env是否存在: {shared_env_dir.exists()}")

            # 检查虚拟环境Python解释器
            venv_python = shared_env_dir / "Scripts" / "python.exe"
            print(f"DEBUG - 虚拟环境Python路径: {venv_python}")
            print(f"DEBUG - 虚拟环境Python是否存在: {venv_python.exists()}")

            # 选择Python解释器
            if venv_python.exists():
                python_executable = str(venv_python)
                print(f"✅ 使用虚拟环境Python: {python_executable}")
            else:
                python_executable = current_python
                print(f"⚠️ 虚拟环境未找到,使用当前Python: {python_executable}")

            # 设置环境变量
            env = os.environ.copy()
            env['PYTHONIOENCODING'] = 'utf-8'

            # 设置正确的PYTHONPATH - 指向src目录
            src_path = project_root / "3_in_1" / "opencv_yolo" / "src"
            print(f"DEBUG - src路径: {src_path}")
            print(f"DEBUG - src路径是否存在: {src_path.exists()}")

            env['PYTHONPATH'] = str(src_path)
            print(f"✅ 设置PYTHONPATH: {src_path}")

            # 设置工作目录为项目根目录
            working_dir = str(project_root)
            print(f"✅ 设置工作目录: {working_dir}")

            process = subprocess.Popen(
                [python_executable, temp_file],
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                encoding='utf-8',
                errors='replace',
                env=env,
                cwd=working_dir
            )

            # 发送调试信息到主线程
            self.output_signal.emit(f"DEBUG - 使用Python解释器: {python_executable}")
            self.output_signal.emit(f"DEBUG - 工作目录: {working_dir}")
            self.output_signal.emit(f"DEBUG - PYTHONPATH: {env.get('PYTHONPATH', 'Not set')}")
            self.output_signal.emit(f"DEBUG - 项目根目录: {project_root}")
            self.output_signal.emit("=" * 50)

            # Read output line by line
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    self.output_signal.emit(output.strip())

            return_code = process.poll()
            self.finished_signal.emit(return_code)

        except Exception as e:
            self.output_signal.emit(f"❌ 执行错误: {e}")
            self.finished_signal.emit(1)
        finally:
            # Clean up temporary file
            if temp_file and os.path.exists(temp_file):
                try:
                    os.unlink(temp_file)
                except:
                    pass


class SourceCodeDialog(QDialog):
    """源代码对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 设置对话框图标
        self.setup_dialog_icon()
        self.parent_window = parent
        self.execution_thread = None

        # 存储GUI状态和静态快照代码
        self.current_gui_state = None
        self.static_snapshot_code = None

        self.init_ui()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowTitle("📄 源代码编辑器")
        self.setGeometry(100, 100, 1200, 800)

        layout = QVBoxLayout()
        self.setLayout(layout)

        # 工具栏
        toolbar_layout = QHBoxLayout()
        layout.addLayout(toolbar_layout)

        # 生成当前状态代码按钮
        self.generate_snapshot_button = QPushButton("📸 生成当前状态代码")
        self.generate_snapshot_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        self.generate_snapshot_button.clicked.connect(self.generate_current_state_code)
        self.generate_snapshot_button.setToolTip("捕获当前GUI状态并生成固化参数的代码")
        toolbar_layout.addWidget(self.generate_snapshot_button)

        # 状态指示器
        self.state_indicator = QLabel("📋 通用模板代码")
        self.state_indicator.setStyleSheet("""
            QLabel {
                color: #7f8c8d;
                font-size: 12px;
                padding: 8px;
                border-radius: 4px;
            }
        """)
        toolbar_layout.addWidget(self.state_indicator)

        toolbar_layout.addStretch()

        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # GUI检测方法复制标签页
        self.create_gui_detector_tab()
        
        # 使用示例标签页
        self.create_usage_example_tab()
        
        layout.addWidget(self.tab_widget)
        
        # 底部按钮
        button_layout = QHBoxLayout()
        layout.addLayout(button_layout)

        # 运行代码按钮
        self.run_button = QPushButton("▶️ 运行代码")
        self.run_button.clicked.connect(self.run_current_code)
        button_layout.addWidget(self.run_button)

        # 复制代码按钮
        self.copy_button = QPushButton("📋 复制代码")
        self.copy_button.clicked.connect(self.copy_current_code)
        button_layout.addWidget(self.copy_button)

        # 保存代码按钮
        self.save_button = QPushButton("💾 保存代码")
        self.save_button.clicked.connect(self.save_current_code)
        button_layout.addWidget(self.save_button)

        # 导出独立脚本按钮
        self.export_button = QPushButton("📦 导出独立脚本")
        self.export_button.clicked.connect(self.export_as_standalone_script)
        button_layout.addWidget(self.export_button)

        button_layout.addStretch()

        # 关闭按钮
        self.close_button = QPushButton("❌ 关闭")
        self.close_button.clicked.connect(self.close)
        button_layout.addWidget(self.close_button)

        # 输出区域
        self.output_text = QTextEdit()
        self.output_text.setMaximumHeight(200)
        self.output_text.setPlaceholderText("代码执行输出将显示在这里...")
        layout.addWidget(self.output_text)

    def setup_dialog_icon(self):
        # 设置对话框图标
        try:
            from PyQt6.QtGui import QIcon

            # 图标文件路径
            icon_path = Path(__file__).parent.parent.parent.parent / "icons" / "yolo_detector_app.ico"

            if icon_path.exists():
                icon = QIcon(str(icon_path))
                self.setWindowIcon(icon)
                print(f"✅ 源代码对话框图标已设置")
            else:
                print(f"⚠️ 图标文件不存在: {icon_path}")

        except Exception as e:
            print(f"⚠️ 设置对话框图标失败: {e}")

    def create_gui_detector_tab(self):
        # 创建GUI检测器标签页
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 代码编辑器
        code_editor = QTextEdit()
        code_editor.setPlainText(self.get_gui_detector_code())
        code_editor.setFont(QFont("Consolas", 10))
        layout.addWidget(code_editor)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "🎯 GUI检测复制")
    
    def create_usage_example_tab(self):
        # 创建使用示例标签页
        tab = QWidget()
        layout = QVBoxLayout()
        
        # 代码编辑器
        code_editor = QTextEdit()
        code_editor.setPlainText(self.get_usage_example_code())
        code_editor.setFont(QFont("Consolas", 10))
        layout.addWidget(code_editor)
        
        tab.setLayout(layout)
        self.tab_widget.addTab(tab, "📚 使用示例")

    def get_gui_detector_code(self) -> str:
        # 获取GUI检测器代码
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# YOLO OpenCV检测器 - GUI方法复制版本
# 完全复制GUI中的检测逻辑

import sys
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent.absolute()
src_path = current_dir / "src"
sys.path.insert(0, str(src_path))

try:
    from yolo_opencv_detector.core.yolo_detector_v2 import YOLODetectorV2
    from yolo_opencv_detector.core.screen_capture_service_v2 import ScreenCaptureServiceV2
    from yolo_opencv_detector.utils.config_manager import ConfigManager
    print("?所有模块导入成?)
except ImportError as e:
    print(f"?模块导入失败: {e}")
    sys.exit(1)

def main():
    # 主函数
    print("🎯 YOLO OpenCV检测器启动")
    
    try:
        # 初始化配置管理器
        config_manager = ConfigManager()
        
        # 初始化检测器
        detector = YOLODetectorV2(config_manager)
        
        # 初始化屏幕截图服?        screen_capture = ScreenCaptureServiceV2()
        
        print("?检测器初始化完?)
        
        # 执行检?        image = screen_capture.capture_screen()
        detections = detector.detect(image)
        
        print(f"🎉 检测完成!发现 {len(detections)} 个目?)
        
        for i, detection in enumerate(detections):
            print(f"目标 {i+1}: {detection}")
            
    except Exception as e:
        print(f"?检测失? {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
'''

    def get_usage_example_code(self) -> str:
        # 获取使用示例代码
        return '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
# YOLO检测器使用示例
# 展示各种检测和自动化操作

print("🎯 YOLO检测器使用示例")
print("=" * 50)

print("📋 基础检测示?")
print("1. 屏幕截图")
print("2. YOLO目标检测")
print("3. 结果处理")

print("\\n🤖 自动化操作示例")
print("1. 智能点击")
print("2. 拖拽操作")
print("3. 多目标处理")

print("\\n✅ 示例代码运行成功!")
'''

    def generate_current_state_code(self):
        # 生成当前GUI状态的代码
        try:
            print("🎯 开始生成当前状态代码...")
            print("=" * 50)

            # 捕获当前GUI状态
            print("📋 步骤1: 捕获当前GUI状态...")
            gui_state = self._capture_current_gui_state()

            if gui_state:
                print("✅ GUI状态捕获成功")
                print(f"   检测模式: {gui_state.get('detection_mode', 'unknown')}")
                print(f"   模板匹配启用: {gui_state.get('template_matching_enabled', False)}")
                print(f"   选中模板: {gui_state.get('selected_template', 'None')}")
                print(f"   模板数据: {gui_state.get('template_data', 'None')}")
            else:
                print("❌ GUI状态捕获失败")

            if gui_state:
                # 生成静态快照代码
                print("\n📋 步骤2: 生成静态快照代码...")
                self.static_snapshot_code = self._generate_static_snapshot_code(gui_state)

                # 检查生成的代码中的关键参数
                print("\n📋 步骤3: 验证生成的代码参数...")
                self._verify_generated_code_parameters()
                self.current_gui_state = gui_state

                # 更新状态指示器
                mode_text = "模板匹配" if gui_state['template_matching_enabled'] else "YOLO检测"
                template_text = f", 模板:{gui_state['selected_template']}" if gui_state['selected_template'] else ""
                status_text = f"📸 当前状态快照(模式:{mode_text}, 置信度:{gui_state['confidence_threshold']:.2f}, NMS:{gui_state['nms_threshold']:.2f}{template_text})"
                self.state_indicator.setText(status_text)
                self.state_indicator.setStyleSheet("QLabel { color: #27ae60; background-color: #d5f4e6; padding: 8px; }")

                # 创建或更新当前状态代码标签页
                self._update_snapshot_tab()

                QMessageBox.information(self, "成功", "✅ 当前状态代码生成成功!\n已添加到新的标签页中.")
                print("✅ 当前状态代码生成完成")
            else:
                QMessageBox.warning(self, "警告", "⚠️ 无法获取当前GUI状态,请确保主窗口正常运行.")

        except Exception as e:
            print(f"❌ 生成当前状态代码失败: {e}")
            QMessageBox.critical(self, "错误", f"生成当前状态代码失败:\n{str(e)}")

    def _update_snapshot_tab(self):
        # 更新或创建当前状态快照标签页
        try:
            if not self.current_gui_state:
                print("⚠️ 没有当前GUI状态数据")
                return

            # 生成静态快照代码
            snapshot_code = self._generate_static_snapshot_code(self.current_gui_state)

            # 查找是否已存在快照标签页
            snapshot_tab_index = -1
            for i in range(self.tab_widget.count()):
                if self.tab_widget.tabText(i).startswith("📸"):
                    snapshot_tab_index = i
                    break

            # 创建代码编辑器
            code_editor = QTextEdit()
            code_editor.setPlainText(snapshot_code)
            code_editor.setFont(QFont("Consolas", 10))
            code_editor.setReadOnly(False)  # 允许编辑

            # 创建标签页布局
            tab = QWidget()
            layout = QVBoxLayout()
            layout.addWidget(code_editor)
            tab.setLayout(layout)

            # 添加或更新标签页
            if snapshot_tab_index >= 0:
                # 更新现有标签页
                self.tab_widget.removeTab(snapshot_tab_index)
                self.tab_widget.insertTab(snapshot_tab_index, tab, "📸 当前状态快照")
                self.tab_widget.setCurrentIndex(snapshot_tab_index)
            else:
                # 添加新标签页
                index = self.tab_widget.addTab(tab, "📸 当前状态快照")
                self.tab_widget.setCurrentIndex(index)

            print("✅ 快照标签页已更新")

        except Exception as e:
            print(f"❌ 更新快照标签页失败: {e}")

    def run_current_code(self):
        # 运行当前代码
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.widget(current_index)

            # 查找代码编辑?            code_editor = None
            for child in current_widget.findChildren(QTextEdit):
                # 确保找到的是代码编辑器,而不是输出区?                if child != self.output_text:
                    code_editor = child
                    break

            if code_editor:
                code = code_editor.toPlainText()

                # 清空输出
                self.output_text.clear()
                self.output_text.append("🚀 开始执行代码...")

                # 创建执行线程
                self.execution_thread = CodeExecutionThread(code)
                self.execution_thread.output_signal.connect(self.on_execution_output)
                self.execution_thread.finished_signal.connect(self.on_execution_finished)
                self.execution_thread.start()
            else:
                QMessageBox.warning(self, "警告", "未找到代码编辑器")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"运行代码失败: {e}")
            import traceback
            traceback.print_exc()

    def on_execution_output(self, output: str):
        # 处理执行输出
        self.output_text.append(output)
        # 自动滚动到底部
        cursor = self.output_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.output_text.setTextCursor(cursor)

        # 强制刷新显示
        self.output_text.repaint()

    def on_execution_finished(self, return_code: int):
        # 处理执行完成
        if return_code == 0:
            self.output_text.append("\\n✅ 代码执行完成")
        else:
            self.output_text.append(f"\\n❌ 代码执行失败 (返回码: {return_code})")

        # 自动滚动到底部
        cursor = self.output_text.textCursor()
        cursor.movePosition(cursor.MoveOperation.End)
        self.output_text.setTextCursor(cursor)

    def copy_current_code(self):
        # 复制当前代码
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.widget(current_index)

            code_editor = current_widget.findChild(QTextEdit)
            if code_editor:
                code = code_editor.toPlainText()

                clipboard = QApplication.clipboard()
                clipboard.setText(code)

                QMessageBox.information(self, "成功", "代码已复制到剪贴板!")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"复制代码失败: {e}")

    def save_current_code(self):
        # 保存当前代码
        try:
            current_index = self.tab_widget.currentIndex()
            current_widget = self.tab_widget.widget(current_index)

            code_editor = current_widget.findChild(QTextEdit)
            if code_editor:
                code = code_editor.toPlainText()

                file_path, _ = QFileDialog.getSaveFileName(
                    self,
                    "保存代码",
                    "detection_code.py",
                    "Python文件 (*.py);;所有文件 (*)"
                )

                if file_path:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(code)

                    QMessageBox.information(self, "成功", f"代码已保存到: {file_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存代码失败: {e}")

    def export_as_standalone_script(self):
        # 导出为独立脚本
        QMessageBox.information(self, "提示", "独立脚本导出功能正在开发中...")

    def _capture_current_gui_state(self) -> dict:
        # 捕获当前GUI状态 - 增强调试版本
        try:
            from datetime import datetime
            print("\n🔍 === GUI状态捕获详细过程 ===")

            # 查找主窗口
            print("📋 步骤1: 查找主窗口...")
            main_window = self._find_main_window()
            if not main_window:
                print("❌ 无法找到主窗口")
                return None

            print(f"✅ 找到主窗口: {type(main_window).__name__}")
            print(f"   窗口标题: {main_window.windowTitle() if hasattr(main_window, 'windowTitle') else 'N/A'}")

            gui_state = {
                'export_timestamp': datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                'confidence_threshold': 0.5,
                'nms_threshold': 0.4,
                'selected_template': None,
                'detection_interval': 1.0,
                'auto_detect': False,
                'detection_mode': 'yolo_detection',  # 默认YOLO检测
                'template_matching_enabled': False,
                'template_data': None
            }

            # 尝试获取检测面板的参数
            try:
                detection_panel = self._get_detection_panel(main_window)
                print(f"🔍 检测面板获取结果: {detection_panel}")
                print(f"🔍 检测面板类型: {type(detection_panel)}")

                if detection_panel:
                    # 打印检测面板的所有属性
                    panel_attrs = [attr for attr in dir(detection_panel) if not attr.startswith('_')]
                    print(f"🔍 检测面板属性: {panel_attrs[:10]}...")  # 只显示前10个
                    # 获取置信度阈值
                    if hasattr(detection_panel, 'confidence_slider'):
                        gui_state['confidence_threshold'] = detection_panel.confidence_slider.value() / 100.0
                        print(f"✅ 置信度阈值: {gui_state['confidence_threshold']}")
                    elif hasattr(detection_panel, 'confidence_threshold_slider'):
                        gui_state['confidence_threshold'] = detection_panel.confidence_threshold_slider.value() / 100.0
                        print(f"✅ 置信度阈值: {gui_state['confidence_threshold']}")

                    # 获取NMS阈?                    if hasattr(detection_panel, 'nms_slider'):
                        gui_state['nms_threshold'] = detection_panel.nms_slider.value() / 100.0
                        print(f"?NMS阈? {gui_state['nms_threshold']}")
                    elif hasattr(detection_panel, 'nms_threshold_slider'):
                        gui_state['nms_threshold'] = detection_panel.nms_threshold_slider.value() / 100.0
                        print(f"?NMS阈? {gui_state['nms_threshold']}")

                    # 获取检测间?                    if hasattr(detection_panel, 'interval_spinbox'):
                        gui_state['detection_interval'] = detection_panel.interval_spinbox.value()
                        print(f"?检测间? {gui_state['detection_interval']}")
                    elif hasattr(detection_panel, 'detection_interval_slider'):
                        gui_state['detection_interval'] = detection_panel.detection_interval_slider.value() / 10.0
                        print(f"?检测间? {gui_state['detection_interval']}")
                    elif hasattr(detection_panel, 'detection_interval_spinbox'):
                        gui_state['detection_interval'] = detection_panel.detection_interval_spinbox.value()
                        print(f"?检测间? {gui_state['detection_interval']}")

                    # 获取自动检测状?                    if hasattr(detection_panel, 'auto_detect_checkbox'):
                        gui_state['auto_detect'] = detection_panel.auto_detect_checkbox.isChecked()
                        print(f"?自动检? {gui_state['auto_detect']}")

                    # 检查智能检测管理器状?                    if hasattr(detection_panel, 'smart_detection_manager'):
                        smart_manager = detection_panel.smart_detection_manager
                        print(f"🧠 智能检测管理器: {smart_manager}")
                        print(f"🧠 模板匹配启用状? {getattr(smart_manager, 'template_matching_enabled', 'N/A')}")
                        print(f"🧠 选中模板: {getattr(smart_manager, 'selected_template', 'N/A')}")

                    print(f"✅ 检测面板参数获取成功")
                else:
                    print("⚠️ 未找到检测面板")
            except Exception as e:
                print(f"⚠️ 检测面板参数获取失? {e}")
                import traceback
                traceback.print_exc()

            # 尝试获取模板面板的参数和检测模式
            print("\n📋 步骤3: 获取模板面板和模板数据...")
            template_panel = None
            selected_template_name = None

            try:
                template_panel = self._get_template_panel(main_window)
                print(f"🔍 模板面板获取结果: {template_panel}")
                print(f"   模板面板类型: {type(template_panel).__name__ if template_panel else 'None'}")

                if template_panel:
                    print("✅ 模板面板获取成功，开始提取模板信息...")
                    # 获取选中的模板 - 这部分比较稳定，优先保存
                    print("   🔍 尝试获取当前选中的模板名称...")

                    # 检查模板面板的属性
                    panel_attrs = []
                    for attr in ['template_list', 'template_combo', 'current_template_name', 'current_template', 'selected_template']:
                        if hasattr(template_panel, attr):
                            panel_attrs.append(attr)
                    print(f"   📋 模板面板可用属性: {panel_attrs}")

                    try:
                        # 方法1: 从template_list获取
                        if hasattr(template_panel, 'template_list') and template_panel.template_list:
                            current_item = template_panel.template_list.currentItem()
                            print(f"   🔍 template_list.currentItem(): {current_item}")
                            if current_item:
                                selected_template_name = current_item.text()
                                print(f"✅ 从template_list获取模板名称: '{selected_template_name}'")

                        # 方法2: 从template_combo获取
                        if not selected_template_name and hasattr(template_panel, 'template_combo') and template_panel.template_combo:
                            current_text = template_panel.template_combo.currentText()
                            print(f"   🔍 template_combo.currentText(): '{current_text}'")
                            if current_text and current_text.strip():
                                selected_template_name = current_text
                                print(f"✅ 从template_combo获取模板名称: '{selected_template_name}'")

                        # 方法3: 从current_template_name获取
                        if not selected_template_name and hasattr(template_panel, 'current_template_name'):
                            current_name = template_panel.current_template_name
                            print(f"   🔍 current_template_name: '{current_name}'")
                            if current_name:
                                selected_template_name = current_name
                                print(f"✅ 从current_template_name获取模板名称: '{selected_template_name}'")

                        # 方法4: 从current_template获取
                        if not selected_template_name and hasattr(template_panel, 'current_template') and template_panel.current_template:
                            current_template = template_panel.current_template
                            print(f"   🔍 current_template: {type(current_template)}")
                            if isinstance(current_template, dict) and 'name' in current_template:
                                selected_template_name = current_template['name']
                                print(f"✅ 从current_template获取模板名称: '{selected_template_name}'")

                        # 立即保存模板名称，避免后续异常导致丢失
                        if selected_template_name and selected_template_name.strip():
                            gui_state['selected_template'] = selected_template_name.strip()
                            print(f"✅ 模板名称已保存到gui_state: '{gui_state['selected_template']}'")
                        else:
                            print("⚠️ 未能获取到有效的模板名称")

                    except Exception as e:
                        print(f"❌ 获取模板名称时出错: {e}")
                        import traceback
                        traceback.print_exc()

                    # 尝试获取检测模式和模板数据 - 这部分可能失败，但不影响基本信息
                    print("   🔍 尝试获取检测模式和模板数据...")
                    try:
                        detection_mode, template_data = self._detect_current_detection_mode(detection_panel, template_panel)
                        gui_state['detection_mode'] = detection_mode
                        gui_state['template_matching_enabled'] = (detection_mode == 'template_matching')
                        gui_state['template_data'] = template_data
                        print(f"✅ 检测模式获取成功: {detection_mode}")
                        print(f"   模板匹配启用: {gui_state['template_matching_enabled']}")
                        print(f"   模板数据: {template_data}")
                    except Exception as e:
                        print(f"❌ 检测模式获取失败: {e}")
                        import traceback
                        traceback.print_exc()

                        # 如果有模板名称，默认启用模板匹配
                        if gui_state['selected_template']:
                            gui_state['detection_mode'] = 'template_matching'
                            gui_state['template_matching_enabled'] = True
                            print(f"🔄 使用默认模板匹配模式（因为有选中模板）")

            except Exception as e:
                print(f"⚠️ 模板面板获取失败: {e}")
                import traceback
                traceback.print_exc()

            # 如果有选中的模板但还没有模板数据，尝试构建基本的模板数据
            if gui_state['selected_template'] and not gui_state.get('template_data'):
                try:
                    print(f"🔄 为选中模板构建基本数据: {gui_state['selected_template']}")

                    # 尝试获取模板图像
                    template_image = None
                    if template_panel:
                        template_image = self._get_template_image_from_panel(template_panel, gui_state['selected_template'])

                    # 构建模板路径 - 移除分类标记
                    clean_template_name = gui_state['selected_template']
                    if '] ' in clean_template_name:
                        clean_template_name = clean_template_name.split('] ', 1)[1]
                        print(f"🔄 清理模板名称: '{gui_state['selected_template']}' -> '{clean_template_name}'")

                    template_path = f"templates/{clean_template_name}.png"

                    # 构建基本模板数据
                    gui_state['template_data'] = {
                        'name': clean_template_name,  # 使用清理后的名称
                        'display_name': gui_state['selected_template'],  # 保留原始显示名称
                        'threshold': 0.8,  # 默认阈值
                        'method': 'TM_CCOEFF_NORMED',  # 默认方法
                        'enabled': True,
                        'image': template_image,
                        'path': template_path
                    }

                    # 确保启用模板匹配
                    gui_state['detection_mode'] = 'template_matching'
                    gui_state['template_matching_enabled'] = True

                    print(f"✅ 基本模板数据构建完成: {gui_state['template_data']['name']}")
                    if template_image is not None:
                        print(f"   包含图像数据: {template_image.shape if hasattr(template_image, 'shape') else type(template_image)}")
                    else:
                        print(f"   仅包含路径信息: {template_path}")

                except Exception as e:
                    print(f"⚠️ 构建基本模板数据失败: {e}")

            # 尝试获取配置面板的模板匹配设置
            try:
                config_panel = self._get_config_panel(main_window)
                if config_panel:
                    print(f"✅ 找到配置面板: {type(config_panel)}")

                    # 检查模板匹配启用状态
                    if hasattr(config_panel, 'enable_template_checkbox'):
                        template_enabled = config_panel.enable_template_checkbox.isChecked()
                        gui_state['template_matching_enabled'] = template_enabled
                        print(f"✅ 模板匹配启用状态: {template_enabled}")

                        if template_enabled:
                            gui_state['detection_mode'] = 'template_matching'

                            # 获取模板匹配参数
                            template_threshold = 0.8
                            template_method = "TM_CCOEFF_NORMED"

                            if hasattr(config_panel, 'template_threshold_slider'):
                                template_threshold = config_panel.template_threshold_slider.value() / 100.0
                                print(f"?模板匹配阈? {template_threshold}")

                            if hasattr(config_panel, 'template_method_combo'):
                                template_method = config_panel.template_method_combo.currentText()
                                print(f"?模板匹配方法: {template_method}")

                            # 构建模板数据 - 需要包含实际的图像数据
                            if gui_state['selected_template']:
                                # 清理模板名称，移除分类前缀
                                clean_template_name = gui_state['selected_template']
                                if '] ' in clean_template_name:
                                    clean_template_name = clean_template_name.split('] ', 1)[1]
                                    print(f"🔄 清理模板名称用于template_data: '{gui_state['selected_template']}' -> '{clean_template_name}'")

                                # 尝试从模板面板获取实际的模板图像
                                template_image = self._get_template_image_from_panel(template_panel, gui_state['selected_template'])

                                # 使用清理后的名称构建模板路径
                                template_path = f"templates/{clean_template_name}.png"

                                gui_state['template_data'] = {
                                    'name': clean_template_name,  # 使用清理后的名称
                                    'display_name': gui_state['selected_template'],  # 保留原始显示名称
                                    'threshold': template_threshold,
                                    'method': template_method,
                                    'enabled': True,
                                    'image': template_image,  # 关键:包含实际图像数据
                                    'path': template_path     # 添加路径信息
                                }

                                if template_image is not None:
                                    print(f"✅ 构建模板数据(含图像): {gui_state['template_data']['name']}")
                                    print(f"   图像尺寸: {template_image.shape if hasattr(template_image, 'shape') else 'unknown'}")
                                else:
                                    print(f"⚠️ 构建模板数据(无图像): {gui_state['template_data']['name']}")
                                    print(f"   模板数据: {gui_state['template_data']}")
                        else:
                            gui_state['detection_mode'] = 'yolo_detection'
                            print("ℹ️ 模板匹配未启用,使用YOLO检测模式")
                else:
                    print("⚠️ 未找到配置面板")
            except Exception as e:
                print(f"⚠️ 配置面板检查失败: {e}")
                import traceback
                traceback.print_exc()

            # 如果没有模板面板,尝试从检测面板获取模式信息
            if gui_state['detection_mode'] == 'yolo_detection' and not gui_state['template_matching_enabled']:
                try:
                    detection_mode, template_data = self._detect_current_detection_mode(detection_panel, None)
                    gui_state['detection_mode'] = detection_mode
                    gui_state['template_matching_enabled'] = (detection_mode == 'template_matching')
                    gui_state['template_data'] = template_data
                    print(f"✅ 从检测面板获取检测模式: {detection_mode}")
                except Exception as e:
                    print(f"⚠️ 检测模式获取失败: {e}")

            # 显示最终的GUI状态
            print("\n" + "=" * 50)
            print("📋 === 最终GUI状态捕获结果 ===")
            print(f"✅ 检测模式: {gui_state['detection_mode']}")
            print(f"✅ 模板匹配启用: {gui_state['template_matching_enabled']}")
            print(f"✅ 选择模板: '{gui_state['selected_template']}'")

            # 详细显示模板数据
            template_data = gui_state['template_data']
            if template_data:
                print(f"✅ 模板数据:")
                print(f"   - 名称: '{template_data.get('name', 'N/A')}'")
                print(f"   - 路径: '{template_data.get('path', 'N/A')}'")
                print(f"   - 阈值: {template_data.get('threshold', 'N/A')}")
                print(f"   - 方法: {template_data.get('method', 'N/A')}")
                print(f"   - 启用: {template_data.get('enabled', 'N/A')}")
                print(f"   - 图像: {template_data.get('image', 'None')}")
            else:
                print(f"❌ 模板数据: None")

            print(f"✅ 置信度阈值: {gui_state['confidence_threshold']}")
            print(f"✅ NMS阈值: {gui_state['nms_threshold']}")
            print(f"✅ 检测间隔: {gui_state['detection_interval']}")
            print(f"✅ 自动检测: {gui_state['auto_detect']}")
            print("=" * 50)

            return gui_state

        except Exception as e:
            print(f"❌ 捕获GUI状态失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _detect_current_detection_mode(self, detection_panel, template_panel):
        # 检测当前GUI的检测模式
        try:
            # 方法1: 检查检测面板的智能检测管理器状态
            if detection_panel and hasattr(detection_panel, 'smart_detection_manager'):
                smart_manager = detection_panel.smart_detection_manager
                if hasattr(smart_manager, 'template_matching_enabled') and smart_manager.template_matching_enabled:
                    # 获取模板数据
                    template_data = None
                    if hasattr(smart_manager, 'selected_template') and smart_manager.selected_template:
                        template_data = smart_manager.selected_template
                        print(f"🎯 检测到模板匹配模式,模板: {template_data.get('name', 'unknown')}")
                        return 'template_matching', template_data

            # 方法2: 检查是否有选中的模板
            if template_panel:
                selected_template = None
                if hasattr(template_panel, 'get_selected_template'):
                    selected_template = template_panel.get_selected_template()
                elif hasattr(template_panel, 'template_list') and template_panel.template_list.currentItem():
                    # 尝试构建模板数据
                    template_name = template_panel.template_list.currentItem().text()
                    if template_name and template_name != "无":
                        # 清理模板名称，移除分类标记
                        clean_name = template_name
                        if '] ' in template_name:
                            clean_name = template_name.split('] ', 1)[1]
                            print(f"🔄 在检测模式中清理模板名称: '{template_name}' -> '{clean_name}'")
                        selected_template = {'name': clean_name, 'display_name': template_name}

                if selected_template and selected_template.get('name'):
                    print(f"🎯 检测到选中模板: {selected_template.get('name')}")
                    # 尝试加载模板图像
                    template_data = self._load_template_data(selected_template)
                    if template_data:
                        return 'template_matching', template_data

            # 方法3: 检查模板文件夹
            templates_dir = Path("templates")
            if templates_dir.exists():
                template_files = list(templates_dir.glob("*.png")) + list(templates_dir.glob("*.jpg"))
                if template_files:
                    print(f"📁 发现 {len(template_files)} 个模板文件,但未明确选中")

            # 默认返回YOLO检测模?            print("🔍 使用YOLO检测模?)
            return 'yolo_detection', None

        except Exception as e:
            print(f"⚠️ 检测模式识别失? {e}")
            return 'yolo_detection', None

    def _load_template_data(self, selected_template):
        # 加载模板数据
        try:
            template_name = selected_template.get('name', '')
            if not template_name:
                return None

            # 尝试从模板目录加?            templates_dir = Path("templates")
            possible_paths = [
                templates_dir / f"{template_name}.png",
                templates_dir / f"{template_name}.jpg",
                templates_dir / template_name
            ]

            for template_path in possible_paths:
                if template_path.exists():
                    import cv2
                    template_image = cv2.imread(str(template_path))
                    if template_image is not None:
                        return {
                            'name': template_name,
                            'image': template_image,
                            'threshold': 0.8,  # 默认阈值
                            'path': str(template_path)
                        }

            print(f"⚠️ 无法加载模板图像: {template_name}")
            return None

        except Exception as e:
            print(f"?加载模板数据失败: {e}")
            return None

    def _find_main_window(self):
        # 查找主窗口
        try:
            print("🔍 开始查找主窗口...")

            # 方法1:直接使用parent_window
            if self.parent_window:
                print(f"?通过parent_window找到主窗? {type(self.parent_window)}")
                parent_attrs = [attr for attr in dir(self.parent_window) if not attr.startswith('_')]
                panel_attrs = [attr for attr in parent_attrs if 'panel' in attr.lower()]
                print(f"🔍 parent_window面板属? {panel_attrs}")
                return self.parent_window

            # 方法2:通过QApplication查找
            print("🔍 通过QApplication查找主窗?..")
            from PyQt6.QtWidgets import QApplication
            app = QApplication.instance()
            if app:
                top_widgets = app.topLevelWidgets()
                print(f"🔍 顶级组件数量: {len(top_widgets)}")

                for i, widget in enumerate(top_widgets):
                    print(f"🔍 顶级组件 {i}: {type(widget)}")
                    if hasattr(widget, 'detection_panel') or hasattr(widget, 'template_panel'):
                        print(f"?通过QApplication找到主窗? {type(widget)}")
                        return widget

            print("⚠️ 无法找到主窗口")
            return None

        except Exception as e:
            print(f"?查找主窗口失? {e}")
            return None

    def _get_detection_panel(self, main_window):
        # 获取检测面板
        try:
            print(f"🔍 主窗口类? {type(main_window)}")
            main_attrs = [attr for attr in dir(main_window) if not attr.startswith('_')]
            print(f"🔍 主窗口属性: {[attr for attr in main_attrs if 'panel' in attr.lower() or 'detection' in attr.lower()]}")

            # 直接属性访问
            if hasattr(main_window, 'detection_panel'):
                panel = main_window.detection_panel
                print(f"✅ 找到 detection_panel: {type(panel)}")
                return panel
            elif hasattr(main_window, 'config_panel'):
                panel = main_window.config_panel
                print(f"?找到 config_panel: {type(panel)}")
                return panel
            elif hasattr(main_window, 'control_panel'):
                panel = main_window.control_panel
                print(f"?找到 control_panel: {type(panel)}")
                return panel

            # 尝试通过findChildren查找DetectionPanelV2
            try:
                from yolo_opencv_detector.gui.widgets.detection_panel_v2 import DetectionPanelV2
                detection_panels = main_window.findChildren(DetectionPanelV2)
                if detection_panels:
                    panel = detection_panels[0]
                    print(f"?通过findChildren找到DetectionPanelV2: {type(panel)}")
                    return panel
            except Exception as e:
                print(f"⚠️ findChildren查找失败: {e}")

            # 尝试查找包含检测功能的面板
            for attr_name in main_attrs:
                if 'panel' in attr_name.lower():
                    panel = getattr(main_window, attr_name)
                    print(f"🔍 检查面板 {attr_name}: {type(panel)}")
                    # 检查是否有检测相关的属性
                    if hasattr(panel, 'confidence_slider') or hasattr(panel, 'smart_detection_manager'):
                        print(f"✅ 找到检测面板: {attr_name}")
                        return panel

            print("⚠️ 未找到检测面板")
            return None

        except Exception as e:
            print(f"?获取检测面板失? {e}")
            import traceback
            traceback.print_exc()
            return None

    def _verify_generated_code_parameters(self):
        """验证生成的代码中的关键参数"""
        try:
            if not self.static_snapshot_code:
                print("❌ 生成的代码为空")
                return

            print("🔍 检查生成代码中的关键参数...")

            # 提取关键参数
            import re

            # 检查模板名称
            template_name_match = re.search(r'EXPORTED_TEMPLATE_NAME = (.+)', self.static_snapshot_code)
            template_name = template_name_match.group(1) if template_name_match else "未找到"
            print(f"   📋 EXPORTED_TEMPLATE_NAME = {template_name}")

            # 检查模板数据
            template_data_match = re.search(r'EXPORTED_TEMPLATE_DATA = (.+)', self.static_snapshot_code)
            template_data = template_data_match.group(1) if template_data_match else "未找到"
            print(f"   📋 EXPORTED_TEMPLATE_DATA = {template_data[:100]}{'...' if len(template_data) > 100 else ''}")

            # 检查模板路径
            template_path_match = re.search(r'EXPORTED_TEMPLATE_IMAGE_PATH = (.+)', self.static_snapshot_code)
            template_path = template_path_match.group(1) if template_path_match else "未找到"
            print(f"   📋 EXPORTED_TEMPLATE_IMAGE_PATH = {template_path}")

            # 检查检测模式
            detection_mode_match = re.search(r'EXPORTED_DETECTION_MODE = "(.+)"', self.static_snapshot_code)
            detection_mode = detection_mode_match.group(1) if detection_mode_match else "未找到"
            print(f"   📋 EXPORTED_DETECTION_MODE = \"{detection_mode}\"")

            # 检查模板匹配启用状态
            template_enabled_match = re.search(r'EXPORTED_TEMPLATE_MATCHING_ENABLED = (.+)', self.static_snapshot_code)
            template_enabled = template_enabled_match.group(1) if template_enabled_match else "未找到"
            print(f"   📋 EXPORTED_TEMPLATE_MATCHING_ENABLED = {template_enabled}")

            # 分析问题
            print("\n🔍 问题分析:")
            if template_name == "None":
                print("❌ 模板名称为None - 模板数据获取失败")
            else:
                print(f"✅ 模板名称正确: {template_name}")

            if template_data == "None":
                print("❌ 模板数据为None - 模板数据构建失败")
            else:
                print(f"✅ 模板数据存在")

            if template_path == "None":
                print("❌ 模板路径为None - 路径构建失败")
            else:
                print(f"✅ 模板路径存在: {template_path}")

            if detection_mode == "template_matching" and template_name == "None":
                print("⚠️ 检测模式为template_matching但模板名称为None - 配置不一致")

        except Exception as e:
            print(f"❌ 验证生成代码参数失败: {e}")

    def _get_template_panel(self, main_window):
        # 获取模板面板 - 增强版本，支持多种查找方式
        try:
            print(f"🔍 开始查找模板面板...")
            print(f"   主窗口类型: {type(main_window).__name__}")

            # 方法1: 直接属性访问
            print("   🔍 方法1: 检查直接属性...")
            direct_attrs = ['template_panel', 'template_widget', 'template_tab', 'template_page']
            for attr in direct_attrs:
                if hasattr(main_window, attr):
                    panel = getattr(main_window, attr)
                    if panel:
                        print(f"✅ 通过属性 '{attr}' 找到模板面板: {type(panel).__name__}")
                        return panel

            # 方法2: 通过findChildren查找TemplatePanelV2
            print("   🔍 方法2: 通过findChildren查找TemplatePanelV2...")
            try:
                from yolo_opencv_detector.gui.widgets.template_panel_v2 import TemplatePanelV2
                template_panels = main_window.findChildren(TemplatePanelV2)
                if template_panels:
                    panel = template_panels[0]
                    print(f"✅ 通过findChildren找到TemplatePanelV2: {type(panel).__name__}")
                    return panel
            except ImportError as e:
                print(f"   ⚠️ 无法导入TemplatePanelV2: {e}")
            except Exception as e:
                print(f"   ⚠️ findChildren查找失败: {e}")

            # 方法3: 通过对象名称查找
            print("   🔍 方法3: 通过对象名称查找...")
            template_names = ['template_panel', 'templatePanel', 'template_widget', 'templateWidget']
            for name in template_names:
                try:
                    panel = main_window.findChild(object, name)
                    if panel:
                        print(f"✅ 通过对象名称 '{name}' 找到模板面板: {type(panel).__name__}")
                        return panel
                except Exception as e:
                    print(f"   ⚠️ 查找对象名称 '{name}' 失败: {e}")

            # 方法4: 遍历所有子控件查找可能的模板面板
            print("   🔍 方法4: 遍历子控件查找...")
            try:
                all_children = main_window.findChildren(object)
                for child in all_children:
                    child_name = child.objectName() if hasattr(child, 'objectName') else ''
                    child_type = type(child).__name__

                    # 检查是否是模板相关的控件
                    if ('template' in child_name.lower() or
                        'template' in child_type.lower() or
                        hasattr(child, 'template_list') or
                        hasattr(child, 'template_combo')):
                        print(f"✅ 通过遍历找到可能的模板面板: {child_type} (名称: '{child_name}')")
                        return child
            except Exception as e:
                print(f"   ⚠️ 遍历子控件失败: {e}")

            print("❌ 所有方法都未找到模板面板")
            return None

        except Exception as e:
            print(f"❌ 获取模板面板失败: {e}")
            import traceback
            traceback.print_exc()
            import traceback
            traceback.print_exc()
            return None

    def _get_config_panel(self, main_window):
        # 获取配置面板
        try:
            print(f"🔍 查找配置面板...")

            # 直接属性访问
            if hasattr(main_window, 'config_panel'):
                panel = main_window.config_panel
                print(f"✅ 找到 config_panel: {type(panel)}")
                return panel

            # 尝试通过findChildren查找ConfigPanelV2
            try:
                from yolo_opencv_detector.gui.widgets.config_panel_v2 import ConfigPanelV2
                config_panels = main_window.findChildren(ConfigPanelV2)
                if config_panels:
                    panel = config_panels[0]
                    print(f"?通过findChildren找到ConfigPanelV2: {type(panel)}")
                    return panel
            except Exception as e:
                print(f"⚠️ findChildren查找配置面板失败: {e}")

            # 尝试查找包含配置功能的面?            main_attrs = [attr for attr in dir(main_window) if not attr.startswith('_')]
            for attr_name in main_attrs:
                if 'config' in attr_name.lower() and 'panel' in attr_name.lower():
                    panel = getattr(main_window, attr_name)
                    print(f"🔍 检查配置面板 {attr_name}: {type(panel)}")
                    # 检查是否有模板匹配相关的属性
                    if hasattr(panel, 'enable_template_checkbox'):
                        print(f"✅ 找到配置面板: {attr_name}")
                        return panel

            print("⚠️ 未找到配置面板")
            return None

        except Exception as e:
            print(f"?获取配置面板失败: {e}")
            import traceback
            traceback.print_exc()
            return None

    def _get_template_image_from_panel(self, template_panel, template_name):
        # 从模板面板获取模板图像 - 改进版，更加健壮
        if not template_panel or not template_name:
            print("⚠️ 模板面板或模板名称为空")
            return None

        print(f"🔍 从模板面板获取模板图像: {template_name}")

        # 尝试多种方法获取模板图像，任何一种成功即可
        methods = [
            self._get_from_current_template,
            self._get_from_template_list,
            self._get_from_template_cache,
            self._get_from_file_system
        ]

        for method in methods:
            try:
                image = method(template_panel, template_name)
                if image is not None:
                    print(f"✅ 成功获取模板图像: {image.shape if hasattr(image, 'shape') else type(image)}")
                    return image
            except Exception as e:
                print(f"⚠️ 方法 {method.__name__} 失败: {e}")
                continue

        print(f"⚠️ 所有方法都无法获取模板图像: {template_name}")
        return None

    def _get_from_current_template(self, template_panel, template_name):
        """从当前模板获取图像"""
        if hasattr(template_panel, 'current_template') and template_panel.current_template:
            current_template = template_panel.current_template
            print(f"🔍 检查current_template: {type(current_template)}")

            # 检查是否有图像数据
            if isinstance(current_template, dict) and 'image' in current_template:
                image = current_template['image']
                if image is not None:
                    return image

            # 尝试从路径加载
            if isinstance(current_template, dict) and 'path' in current_template:
                return self._load_template_image_from_path(current_template['path'])

        return None

    def _get_from_template_list(self, template_panel, template_name):
        """从模板列表获取图像"""
        if hasattr(template_panel, 'templates'):
            for template in template_panel.templates:
                if isinstance(template, dict):
                    # 检查模板名称匹配
                    template_display_name = f"[{template.get('category', '未分类')}] {template.get('name', '')}"
                    if (template.get('name') == template_name or
                        template_display_name == template_name):

                        # 尝试获取图像
                        if 'image' in template and template['image'] is not None:
                            return template['image']
                        elif 'path' in template:
                            return self._load_template_image_from_path(template['path'])
        return None

    def _get_from_template_cache(self, template_panel, template_name):
        """从模板缓存获取图像"""
        if hasattr(template_panel, 'template_cache'):
            cache = template_panel.template_cache
            if isinstance(cache, dict) and template_name in cache:
                cached_template = cache[template_name]
                if isinstance(cached_template, dict) and 'image' in cached_template:
                    return cached_template['image']
        return None

    def _get_from_file_system(self, template_panel, template_name):
        """从文件系统获取图像"""
        # 清理模板名称，移除分类标记
        clean_name = template_name
        if '] ' in template_name:
            clean_name = template_name.split('] ', 1)[1]
            print(f"🔄 清理文件系统查找的模板名称: '{template_name}' -> '{clean_name}'")

        # 尝试常见的模板文件路径
        possible_paths = [
            f"templates/{clean_name}.png",
            f"templates/{clean_name}.jpg",
            f"templates/{clean_name}.jpeg",
            f"assets/templates/{clean_name}.png",
            f"resources/templates/{clean_name}.png"
        ]

        # 首先尝试精确匹配
        for path in possible_paths:
            image = self._load_template_image_from_path(path)
            if image is not None:
                print(f"✅ 从文件系统加载(精确匹配): {path}")
                return image

        # 如果精确匹配失败，尝试模糊匹配（包含时间戳的文件名）
        try:
            from pathlib import Path
            templates_dir = Path("templates")
            if templates_dir.exists():
                print(f"🔍 在templates目录中查找包含'{clean_name}'的文件...")
                for file_path in templates_dir.glob("*.png"):
                    if clean_name in file_path.stem:
                        print(f"🔍 找到候选文件: {file_path}")
                        image = self._load_template_image_from_path(str(file_path))
                        if image is not None:
                            print(f"✅ 从文件系统加载(模糊匹配): {file_path}")
                            return image

                # 也尝试其他图像格式
                for ext in [".jpg", ".jpeg", ".bmp", ".tiff"]:
                    for file_path in templates_dir.glob(f"*{ext}"):
                        if clean_name in file_path.stem:
                            print(f"🔍 找到候选文件: {file_path}")
                            image = self._load_template_image_from_path(str(file_path))
                            if image is not None:
                                print(f"✅ 从文件系统加载(模糊匹配): {file_path}")
                                return image
        except Exception as e:
            print(f"⚠️ 模糊匹配搜索失败: {e}")

        return None

    def _load_template_image_from_path(self, template_path):
        """从路径加载模板图像"""
        try:
            import cv2
            import numpy as np
            from pathlib import Path

            if not template_path:
                return None

            path = Path(template_path)
            if not path.exists():
                print(f"⚠️ 模板文件不存在: {template_path}")
                return None

            # 使用支持中文文件名的方法加载
            image_data = np.fromfile(str(path), dtype=np.uint8)
            image = cv2.imdecode(image_data, cv2.IMREAD_COLOR)

            if image is not None:
                print(f"✅ 成功加载模板图像: {template_path}")
                return image
            else:
                print(f"⚠️ 无法解码图像文件: {template_path}")
                return None

        except Exception as e:
            print(f"❌ 加载模板图像失败: {e}")
            return None
    def _load_template_image_by_name(self, template_name):
        """根据模板名称从文件夹加载图像"""
        try:
            import cv2
            import numpy as np
            from pathlib import Path

            # 清理模板名称，移除分类标记
            clean_name = template_name
            if '] ' in template_name:
                clean_name = template_name.split('] ', 1)[1]
                print(f"🔄 清理模板名称用于文件加载: '{template_name}' -> '{clean_name}'")

            # 尝试常见的模板文件路径
            possible_paths = [
                f"templates/{clean_name}.png",
                f"templates/{clean_name}.jpg",
                f"templates/{clean_name}.jpeg",
                f"assets/templates/{clean_name}.png",
                f"resources/templates/{clean_name}.png"
            ]

            # 首先尝试精确匹配
            for template_path in possible_paths:
                image = self._load_template_image_from_path(template_path)
                if image is not None:
                    print(f"✅ 从文件系统加载模板(精确匹配): {template_path}")
                    return image

            # 如果精确匹配失败，尝试模糊匹配（包含时间戳的文件名）
            try:
                templates_dir = Path("templates")
                if templates_dir.exists():
                    print(f"🔍 在templates目录中查找包含'{clean_name}'的文件...")
                    for file_path in templates_dir.glob("*.png"):
                        if clean_name in file_path.stem:
                            print(f"🔍 找到候选文件: {file_path}")
                            image = self._load_template_image_from_path(str(file_path))
                            if image is not None:
                                print(f"✅ 从文件系统加载模板(模糊匹配): {file_path}")
                                return image

                    # 也尝试其他图像格式
                    for ext in [".jpg", ".jpeg", ".bmp", ".tiff"]:
                        for file_path in templates_dir.glob(f"*{ext}"):
                            if clean_name in file_path.stem:
                                print(f"🔍 找到候选文件: {file_path}")
                                image = self._load_template_image_from_path(str(file_path))
                                if image is not None:
                                    print(f"✅ 从文件系统加载模板(模糊匹配): {file_path}")
                                    return image
            except Exception as e:
                print(f"⚠️ 模糊匹配搜索失败: {e}")

            print(f"⚠️ 无法从文件系统加载模板: {template_name}")
            return None

        except Exception as e:
            print(f"❌ 根据名称加载模板失败: {e}")
            return None

    def _generate_static_snapshot_code(self, gui_state: dict) -> str:
        # 生成静态快照代码 - 直接复刻GUI实时检测逻辑

        # 处理模板名称 - 使用清理后的名称用于代码生成
        template_name_for_code = gui_state['selected_template']
        print(f"🔍 代码生成 - 原始模板名称: '{template_name_for_code}'")

        if template_name_for_code and gui_state.get('template_data') and gui_state['template_data'].get('name'):
            # 优先使用template_data中的清理后名称
            template_name_for_code = gui_state['template_data']['name']
            print(f"✅ 代码生成 - 使用template_data中的清理后名称: '{template_name_for_code}'")
        elif template_name_for_code and '] ' in template_name_for_code:
            # 如果template_data中没有清理后的名称，在这里进行清理
            template_name_for_code = template_name_for_code.split('] ', 1)[1]
            print(f"🔄 代码生成 - 手动清理模板名称: '{gui_state['selected_template']}' -> '{template_name_for_code}'")

        template_info = f"'{template_name_for_code}'" if template_name_for_code else "None"
        print(f"📋 代码生成 - 最终template_info: {template_info}")

        # 处理模板图像数据的序列化
        template_data_code = "None"
        if gui_state['template_data'] and gui_state['template_data'].get('image') is not None:
            # 将图像数据转换为可序列化的格式
            template_data_for_code = gui_state['template_data'].copy()
            image = template_data_for_code.pop('image')  # 移除图像数据

            # 添加图像信息用于调试
            template_data_for_code['image_shape'] = image.shape if hasattr(image, 'shape') else 'unknown'
            template_data_for_code['image_type'] = str(type(image))
            template_data_for_code['has_image'] = True

            template_data_code = repr(template_data_for_code)
        elif gui_state['template_data']:
            template_data_code = repr(gui_state['template_data'])

        # 生成简化的静态代码,避免f-string反斜杠问题
        template_status = '启用' if gui_state['template_matching_enabled'] else '禁用'
        selected_template = gui_state['selected_template'] or '无'

        # 构建代码字符串
        code_parts = []

        # 头部信息
        # 生成简化的代码
        timestamp = gui_state['export_timestamp']
        confidence = gui_state['confidence_threshold']
        nms = gui_state['nms_threshold']
        mode = gui_state['detection_mode']
        template_enabled = gui_state['template_matching_enabled']

        # 使用字符串拼接避免f-string中的三引号问题
        header_lines = [
            "#!/usr/bin/env python3",
            "# -*- coding: utf-8 -*-",
            '\"\"\"',
            "YOLO检测器 - 直接复刻版本",
            f"导出时间: {timestamp}",
            "",
            "这是直接复刻GUI实时检测逻辑的静态版本, 确保100%一致性.",
            "核心逻辑来自 DetectionPanelV2._perform_detection() 方法.",
            "",
            "注意: 此代码直接复刻GUI检测逻辑, 保持与GUI完全相同的检测模式.",
            "如需不同参数, 请重新生成新的状态快照.",
            '\"\"\"',
            ""
        ]

        code_lines = header_lines + [


            "import sys",
            "import os",
            "import time",
            "from pathlib import Path",
            "import cv2",
            "import numpy as np",
            "",
            "# 固化的GUI参数",
            f"EXPORTED_CONFIDENCE_THRESHOLD = {confidence:.3f}",
            f"EXPORTED_NMS_THRESHOLD = {nms:.3f}",
            f'EXPORTED_DETECTION_MODE = "{mode}"',
            f"EXPORTED_TEMPLATE_MATCHING_ENABLED = {template_enabled}",
            f"EXPORTED_TEMPLATE_NAME = {template_info}",
            f"EXPORTED_TEMPLATE_DATA = {template_data_code}",
            f"EXPORTED_DETECTION_INTERVAL = {gui_state['detection_interval']:.1f}",
            f"EXPORTED_AUTO_DETECT = {gui_state['auto_detect']}",
            "",
            "def main():",
            '    print("🎯 YOLO检测器 - 直接复刻版本")',
            '    print(f"📊 检测模式: {EXPORTED_DETECTION_MODE}")',
            '    print(f"📊 固化参数: 置信度={EXPORTED_CONFIDENCE_THRESHOLD}, NMS={EXPORTED_NMS_THRESHOLD}")',
            '    print("✅ 代码生成成功!")',
            "",
            'if __name__ == "__main__":',
            "    main()"
        ]

        # 替换为完整的功能代码
        return self._build_complete_detection_code(
            timestamp, confidence, nms, mode, template_enabled,
            template_info, template_data_code, gui_state
        )

    def _build_complete_detection_code(self, timestamp, confidence, nms, mode,
                                     template_enabled, template_info, template_data_code, gui_state):
        """构建完整的检测功能代码"""

        # 获取模板图像路径
        template_image_path = "None"
        if gui_state.get('template_data'):
            # 尝试多种方式获取模板路径
            template_data = gui_state['template_data']
            if template_data.get('path'):
                template_image_path = f"'{template_data['path']}'"
            elif template_data.get('name'):
                # 使用template_data中的清理后名称
                clean_name = template_data['name']
                template_image_path = f"'templates/{clean_name}.png'"
            elif gui_state.get('selected_template'):
                # 如果没有路径，尝试根据模板名称构建路径（需要清理分类标记）
                template_name = gui_state['selected_template']
                if '] ' in template_name:
                    template_name = template_name.split('] ', 1)[1]
                template_image_path = f"'templates/{template_name}.png'"

        code_lines = [
            "#!/usr/bin/env python3",
            "# -*- coding: utf-8 -*-",
            '"""',
            "YOLO检测器 - 完整功能版本",
            f"导出时间: {timestamp}",
            "",
            "这是完全复刻GUI检测逻辑的独立脚本，包含完整的检测功能。",
            "核心逻辑来自 DetectionPanelV2._perform_detection() 方法。",
            "",
            "功能特性:",
            "- 完整的YOLO目标检测",
            "- 模板匹配功能",
            "- 屏幕截图和图像处理",
            "- 智能检测结果处理",
            "- 错误处理和日志记录",
            '"""',
            "",
            "import sys",
            "import os",
            "import time",
            "import logging",
            "from pathlib import Path",
            "from typing import List, Dict, Any, Optional, Tuple",
            "from dataclasses import dataclass",
            "",
            "try:",
            "    import cv2",
            "    import numpy as np",
            "    CV2_AVAILABLE = True",
            "except ImportError:",
            "    print('⚠️ OpenCV未安装，某些功能可能不可用')",
            "    CV2_AVAILABLE = False",
            "",
            "try:",
            "    import mss",
            "    MSS_AVAILABLE = True",
            "except ImportError:",
            "    MSS_AVAILABLE = False",
            "",
            "try:",
            "    from PIL import ImageGrab",
            "    PIL_AVAILABLE = True",
            "except ImportError:",
            "    PIL_AVAILABLE = False",
            "",
            "try:",
            "    from ultralytics import YOLO",
            "    YOLO_AVAILABLE = True",
            "except ImportError:",
            "    print('⚠️ Ultralytics YOLO未安装，YOLO检测功能不可用')",
            "    YOLO_AVAILABLE = False",
            "",
            "# ==================== 固化的GUI参数 ====================",
            f"EXPORTED_CONFIDENCE_THRESHOLD = {confidence:.3f}",
            f"EXPORTED_NMS_THRESHOLD = {nms:.3f}",
            f'EXPORTED_DETECTION_MODE = "{mode}"',
            f"EXPORTED_TEMPLATE_MATCHING_ENABLED = {template_enabled}",
            f"EXPORTED_TEMPLATE_NAME = {template_info}",
            f"EXPORTED_TEMPLATE_DATA = {template_data_code}",
            f"EXPORTED_TEMPLATE_IMAGE_PATH = {template_image_path}",
            f"EXPORTED_DETECTION_INTERVAL = {gui_state['detection_interval']:.1f}",
            f"EXPORTED_AUTO_DETECT = {gui_state['auto_detect']}",
            "",
            "# ==================== 数据结构 ====================",
            "@dataclass",
            "class DetectionResult:",
            '    """检测结果数据类"""',
            "    bbox: Tuple[int, int, int, int]  # (x, y, w, h)",
            "    confidence: float",
            "    class_id: int",
            "    class_name: str",
            "    timestamp: float",
            "",
            "    def center(self) -> Tuple[float, float]:",
            '        """计算边界框中心点"""',
            "        x, y, w, h = self.bbox",
            "        return (x + w / 2, y + h / 2)",
            "",
            "# ==================== 日志配置 ====================",
            "def setup_logging():",
            '    """设置日志配置"""',
            "    logging.basicConfig(",
            "        level=logging.INFO,",
            "        format='%(asctime)s | %(levelname)s | %(message)s',",
            "        handlers=[",
            "            logging.StreamHandler(),",
            "            logging.FileHandler('detection_log.txt', encoding='utf-8')",
            "        ]",
            "    )",
            "    return logging.getLogger(__name__)",
            "",
            "logger = setup_logging()",
            ""
        ]

        # 添加屏幕截图功能
        code_lines.extend([
            "# ==================== 屏幕截图功能 ====================",
            "class ScreenCapture:",
            '    """屏幕截图类 - 复刻GUI中的ScreenCapture功能"""',
            "",
            "    def __init__(self):",
            "        self.available_methods = []",
            "        if MSS_AVAILABLE:",
            "            self.available_methods.append('mss')",
            "        if PIL_AVAILABLE:",
            "            self.available_methods.append('pil')",
            "",
            "        if not self.available_methods:",
            "            logger.error('没有可用的截图方法')",
            "",
            "    def capture_fullscreen(self) -> Optional[np.ndarray]:",
            '        """截取全屏 - 复刻GUI中的capture_fullscreen方法"""',
            "        if not self.available_methods:",
            "            logger.error('没有可用的截图方法')",
            "            return None",
            "",
            "        method = self.available_methods[0]",
            "        try:",
            "            if method == 'mss':",
            "                return self._capture_with_mss()",
            "            elif method == 'pil':",
            "                return self._capture_with_pil()",
            "            else:",
            "                logger.error(f'未知的截图方法: {method}')",
            "                return None",
            "        except Exception as e:",
            "            logger.error(f'截图失败: {e}')",
            "            return None",
            "",
            "    def _capture_with_mss(self) -> Optional[np.ndarray]:",
            '        """使用MSS截图"""',
            "        if not MSS_AVAILABLE or not CV2_AVAILABLE:",
            "            return None",
            "",
            "        try:",
            "            with mss.mss() as sct:",
            "                monitor = sct.monitors[0]  # 所有显示器的组合",
            "                screenshot = sct.grab(monitor)",
            "                img_array = np.array(screenshot)",
            "                img_bgr = cv2.cvtColor(img_array, cv2.COLOR_BGRA2BGR)",
            "                return img_bgr",
            "        except Exception as e:",
            "            logger.error(f'MSS截图失败: {e}')",
            "            return None",
            "",
            "    def _capture_with_pil(self) -> Optional[np.ndarray]:",
            '        """使用PIL截图"""',
            "        if not PIL_AVAILABLE or not CV2_AVAILABLE:",
            "            return None",
            "",
            "        try:",
            "            screenshot = ImageGrab.grab()",
            "            img_array = np.array(screenshot)",
            "            img_bgr = cv2.cvtColor(img_array, cv2.COLOR_RGB2BGR)",
            "            return img_bgr",
            "        except Exception as e:",
            "            logger.error(f'PIL截图失败: {e}')",
            "            return None",
            ""
        ])

        # 添加YOLO检测功能
        code_lines.extend([
            "# ==================== YOLO检测功能 ====================",
            "class YOLODetector:",
            '    """YOLO检测器类 - 复刻GUI中的YOLODetector功能"""',
            "",
            "    def __init__(self, model_path: str = 'yolov8n.pt'):",
            "        self.model_path = model_path",
            "        self.model = None",
            "        self.is_loaded = False",
            "        self.class_names = []",
            "",
            "    def load_model(self) -> bool:",
            '        """加载YOLO模型"""',
            "        if not YOLO_AVAILABLE:",
            "            logger.error('Ultralytics YOLO未安装')",
            "            return False",
            "",
            "        try:",
            "            logger.info(f'正在加载YOLO模型: {self.model_path}')",
            "            self.model = YOLO(self.model_path)",
            "            self.class_names = self.model.names",
            "            self.is_loaded = True",
            "            logger.info(f'YOLO模型加载成功，支持 {len(self.class_names)} 个类别')",
            "            return True",
            "        except Exception as e:",
            "            logger.error(f'加载YOLO模型失败: {e}')",
            "            self.is_loaded = False",
            "            return False",
            "",
            "    def detect(self, image: np.ndarray, confidence: float = 0.5, ",
            "               nms_threshold: float = 0.4) -> List[Dict[str, Any]]:",
            '        """执行YOLO检测 - 复刻GUI中的detect方法"""',
            "        if not self.is_loaded:",
            "            if not self.load_model():",
            "                logger.error('YOLO模型未加载')",
            "                return []",
            "",
            "        try:",
            "            start_time = time.time()",
            "            results = self.model(",
            "                image,",
            "                conf=confidence,",
            "                iou=nms_threshold,",
            "                verbose=False",
            "            )",
            "",
            "            detections = []",
            "            for result in results:",
            "                if hasattr(result, 'boxes') and result.boxes is not None:",
            "                    boxes = result.boxes",
            "                    if hasattr(boxes, 'xyxy') and boxes.xyxy is not None:",
            "                        xyxy = boxes.xyxy.cpu().numpy()",
            "                        confidences = boxes.conf.cpu().numpy() if hasattr(boxes, 'conf') else []",
            "                        classes = boxes.cls.cpu().numpy() if hasattr(boxes, 'cls') else []",
            "",
            "                        for i in range(len(xyxy)):",
            "                            x1, y1, x2, y2 = xyxy[i]",
            "                            class_id = int(classes[i]) if i < len(classes) else 0",
            "                            class_name = self.class_names.get(class_id, f'class_{class_id}')",
            "",
            "                            detection = {",
            "                                'source': 'yolo',",
            "                                'bbox': [int(x1), int(y1), int(x2 - x1), int(y2 - y1)],",
            "                                'confidence': float(confidences[i]) if i < len(confidences) else 0.0,",
            "                                'class_id': class_id,",
            "                                'class_name': class_name",
            "                            }",
            "                            detections.append(detection)",
            "",
            "            inference_time = time.time() - start_time",
            "            logger.debug(f'YOLO检测完成 - 检测到 {len(detections)} 个目标，耗时 {inference_time:.4f}s')",
            "            return detections",
            "",
            "        except Exception as e:",
            "            logger.error(f'YOLO检测失败: {e}')",
            "            return []",
            ""
        ])

        # 添加模板匹配功能
        code_lines.extend([
            "# ==================== 模板匹配功能 ====================",
            "class TemplateMatcherV2:",
            '    """模板匹配器类 - 复刻GUI中的TemplateMatcherV2功能"""',
            "",
            "    def __init__(self, threshold: float = 0.8):",
            "        self.threshold = threshold",
            "        self.method = cv2.TM_CCOEFF_NORMED if CV2_AVAILABLE else None",
            "",
            "    def load_template_from_path(self, template_path: str) -> Optional[np.ndarray]:",
            '        """从路径加载模板图像"""',
            "        if not CV2_AVAILABLE:",
            "            logger.error('OpenCV未安装，无法加载模板')",
            "            return None",
            "",
            "        try:",
            "            if not os.path.exists(template_path):",
            "                logger.error(f'模板文件不存在: {template_path}')",
            "                return None",
            "",
            "            template = cv2.imread(template_path)",
            "            if template is None:",
            "                logger.error(f'无法读取模板文件: {template_path}')",
            "                return None",
            "",
            "            logger.info(f'模板加载成功: {template_path}, 尺寸: {template.shape}')",
            "            return template",
            "",
            "        except Exception as e:",
            "            logger.error(f'加载模板失败: {e}')",
            "            return None",
            "",
            "    def match_template(self, image: np.ndarray, template: np.ndarray,",
            "                      threshold: Optional[float] = None) -> List[Dict[str, Any]]:",
            '        """执行模板匹配 - 复刻GUI中的SmartDetectionManager._perform_template_matching方法"""',
            "        if not CV2_AVAILABLE:",
            "            logger.error('OpenCV未安装，无法执行模板匹配')",
            "            return []",
            "",
            "        if image is None or template is None:",
            "            logger.error('输入图像或模板为空')",
            "            return []",
            "",
            "        match_threshold = threshold if threshold is not None else self.threshold",
            "",
            "        try:",
            "            # 执行模板匹配",
            "            result = cv2.matchTemplate(image, template, self.method)",
            "",
            "            # 找到所有匹配位置",
            "            locations = np.where(result >= match_threshold)",
            "",
            "            matches = []",
            "            h, w = template.shape[:2]",
            "",
            "            for pt in zip(*locations[::-1]):  # 切换x和y坐标",
            "                matches.append({",
            "                    'source': 'template',",
            "                    'bbox': [pt[0], pt[1], w, h],",
            "                    'confidence': float(result[pt[1], pt[0]]),",
            "                    'class_name': 'template_match',",
            "                    'class_id': -1  # 模板匹配使用特殊ID",
            "                })",
            "",
            "            logger.debug(f'模板匹配找到 {len(matches)} 个匹配')",
            "            return matches",
            "",
            "        except Exception as e:",
            "            logger.error(f'模板匹配失败: {e}')",
            "            return []",
            ""
        ])

        # 添加智能检测管理器和主检测逻辑
        code_lines.extend([
            "# ==================== 智能检测管理器 ====================",
            "class SmartDetectionManager:",
            '    """智能检测管理器 - 复刻GUI中的SmartDetectionManager功能"""',
            "",
            "    def __init__(self):",
            "        self.template_matching_enabled = EXPORTED_TEMPLATE_MATCHING_ENABLED",
            "        self.selected_template = None",
            "        self.template_matcher = TemplateMatcherV2()",
            "        self.detection_count = 0",
            "",
            "        # 加载模板",
            "        if self.template_matching_enabled and EXPORTED_TEMPLATE_IMAGE_PATH not in ['None', None]:",
            "            try:",
            "                template_path = str(EXPORTED_TEMPLATE_IMAGE_PATH).strip(\"'\")",
            "                self.selected_template = self.template_matcher.load_template_from_path(template_path)",
            "                if self.selected_template is None:",
            "                    logger.warning(f'模板加载失败，将禁用模板匹配: {template_path}')",
            "                    self.template_matching_enabled = False",
            "            except Exception as e:",
            "                logger.error(f'模板加载异常，将禁用模板匹配: {e}')",
            "                self.template_matching_enabled = False",
            "        else:",
            "            logger.info('模板匹配未启用或模板路径无效')",
            "",
            "    def process_detections(self, image: np.ndarray, detections: List[Dict[str, Any]]) -> Dict[str, Any]:",
            '        """处理检测结果 - 复刻GUI中的SmartDetectionManager.process_detections方法"""',
            "        self.detection_count += 1",
            "        current_time = time.time()",
            "",
            "        # 如果启用了模板匹配，执行模板匹配",
            "        template_matches = []",
            "        if self.template_matching_enabled and self.selected_template is not None:",
            "            template_matches = self.template_matcher.match_template(image, self.selected_template)",
            "",
            "        # 决定使用哪种检测结果",
            "        if self.template_matching_enabled:",
            "            # 模板匹配模式：只使用模板匹配结果",
            "            detection_source = template_matches",
            "            logger.debug(f'使用模板匹配结果: {len(template_matches)} 个匹配')",
            "        else:",
            "            # YOLO检测模式：使用YOLO检测结果",
            "            detection_source = detections",
            "            logger.debug(f'使用YOLO检测结果: {len(detections)} 个检测')",
            "",
            "        # 转换检测结果格式",
            "        current_detections = []",
            "        for det in detection_source:",
            "            try:",
            "                bbox = det.get('bbox', [])",
            "                if len(bbox) == 4:",
            "                    result = DetectionResult(",
            "                        bbox=tuple(bbox),",
            "                        confidence=det.get('confidence', 0.0),",
            "                        class_id=det.get('class_id', 0),",
            "                        class_name=det.get('class_name', 'unknown'),",
            "                        timestamp=current_time",
            "                    )",
            "                    current_detections.append(result)",
            "            except Exception as e:",
            "                logger.warning(f'解析检测结果失败: {e}')",
            "",
            "        # 处理结果",
            "        result = {",
            "            'should_save': len(current_detections) > 0,",
            "            'should_overlay': len(current_detections) > 0,",
            "            'is_duplicate': False,",
            "            'detections': current_detections,",
            "            'detection_mode': 'template_matching' if self.template_matching_enabled else 'yolo_detection'",
            "        }",
            "",
            "        return result",
            ""
        ])

        # 添加主检测器类
        code_lines.extend([
            "# ==================== 主检测器类 ====================",
            "class GUIDetectorCopy:",
            '    """GUI检测器复刻版 - 完全复刻DetectionPanelV2._perform_detection()方法"""',
            "",
            "    def __init__(self):",
            "        self.screen_capture = ScreenCapture()",
            "        self.yolo_detector = YOLODetector()",
            "        self.smart_detection_manager = SmartDetectionManager()",
            "        self.detection_count = 0",
            "",
            "        # 初始化YOLO检测器",
            "        self.yolo_available = False",
            "        # 总是尝试初始化YOLO作为备用检测方法",
            "        logger.info('正在初始化YOLO检测器...')",
            "        if self.yolo_detector.load_model():",
            "            self.yolo_available = True",
            "            logger.info('YOLO检测器初始化成功')",
            "        else:",
            "            logger.warning('YOLO模型加载失败 - 请确保已安装ultralytics并下载模型文件')",
            "",
            "        # 检查可用的检测方法",
            "        template_available = (self.smart_detection_manager.template_matching_enabled and ",
            "                            self.smart_detection_manager.selected_template is not None)",
            "        ",
            "        if not self.yolo_available and not template_available:",
            "            logger.error('警告：YOLO和模板匹配都不可用，检测功能将受限')",
            "            logger.info('建议：')",
            "            logger.info('  1. 安装ultralytics: pip install ultralytics')",
            "            logger.info('  2. 或配置有效的模板文件')",
            "        elif template_available and EXPORTED_DETECTION_MODE == 'template_matching':",
            "            logger.info('将使用模板匹配进行检测')",
            "        elif self.yolo_available:",
            "            logger.info('将使用YOLO进行检测')",
            "        else:",
            "            logger.info('将尝试可用的检测方法')",
            "",
            "    def perform_detection(self) -> Dict[str, Any]:",
            '        """执行一次检测 - 完全复刻DetectionPanelV2._perform_detection()方法"""',
            "        try:",
            "            start_time = time.time()",
            "",
            "            # 步骤1: 截取屏幕 (复刻GUI第933行)",
            "            logger.info('正在截取屏幕...')",
            "            image = self.screen_capture.capture_fullscreen()",
            "",
            "            if image is None:",
            "                logger.error('截取屏幕失败')",
            "                return {'success': False, 'error': '截取屏幕失败'}",
            "",
            "            logger.info(f'屏幕截取成功，图像尺寸: {image.shape}')",
            "",
            "            # 步骤2: 执行YOLO检测 (复刻GUI第948-952行)",
            "            detections = []",
            "            # 智能选择检测方法：优先使用配置的方法，如果不可用则自动降级",
            "            template_available = (self.smart_detection_manager.template_matching_enabled and",
            "                                self.smart_detection_manager.selected_template is not None)",
            "            ",
            "            # 检测方法选择逻辑",
            "            if EXPORTED_DETECTION_MODE == 'template_matching' and template_available:",
            "                use_yolo = False",
            "                logger.info('使用配置的模板匹配检测')",
            "            elif EXPORTED_DETECTION_MODE == 'yolo_detection' and self.yolo_available:",
            "                use_yolo = True",
            "                logger.info('使用配置的YOLO检测')",
            "            elif self.yolo_available:",
            "                use_yolo = True",
            "                logger.info('模板匹配不可用，自动降级到YOLO检测')",
            "            elif template_available:",
            "                use_yolo = False",
            "                logger.info('YOLO不可用，尝试使用模板匹配')",
            "            else:",
            "                use_yolo = False",
            "                logger.warning('所有检测方法都不可用，将返回空结果')",
            "            ",
            "            if use_yolo and self.yolo_available:",
            "                try:",
            "                    logger.info('正在执行YOLO检测...')",
            "                    detections = self.yolo_detector.detect(",
            "                        image,",
            "                        confidence=EXPORTED_CONFIDENCE_THRESHOLD,",
            "                        nms_threshold=EXPORTED_NMS_THRESHOLD",
            "                    )",
            "                    logger.info(f'YOLO检测完成，发现 {len(detections)} 个目标')",
            "                except Exception as e:",
            "                    logger.error(f'YOLO检测失败: {e}')",
            "            elif not use_yolo:",
            "                logger.info('将使用模板匹配进行检测')",
            "            else:",
            "                logger.warning('YOLO检测不可用，将尝试模板匹配')",
            "",
            "            # 步骤3: 智能检测处理 (复刻GUI第965行)",
            "            detection_result = self.smart_detection_manager.process_detections(image, detections)",
            "",
            "            # 步骤4: 保存结果",
            "            if detection_result.get('should_save', False):",
            "                save_path = self._save_screenshot(image, detection_result['detections'])",
            "                detection_result['save_path'] = save_path",
            "",
            "            # 更新统计",
            "            self.detection_count += 1",
            "            total_time = time.time() - start_time",
            "",
            "            # 返回结果",
            "            result = {",
            "                'success': True,",
            "                'detections': detection_result['detections'],",
            "                'detection_mode': detection_result['detection_mode'],",
            "                'should_save': detection_result.get('should_save', False),",
            "                'save_path': detection_result.get('save_path'),",
            "                'detection_count': self.detection_count,",
            "                'processing_time': total_time,",
            "                'image_shape': image.shape",
            "            }",
            "",
            "            logger.info(f'检测完成 - 模式: {result[\"detection_mode\"]}, '",
            "                       f'目标数: {len(result[\"detections\"])}, '",
            "                       f'耗时: {total_time:.3f}s')",
            "",
            "            return result",
            "",
            "        except Exception as e:",
            "            logger.error(f'检测过程失败: {e}')",
            "            return {'success': False, 'error': str(e)}",
            ""
        ])

        # 添加保存功能和主函数
        code_lines.extend([
            "    def _save_screenshot(self, image: np.ndarray, detections: List[DetectionResult]) -> Optional[str]:",
            '        """保存截图和检测结果"""',
            "        try:",
            "            # 创建保存目录",
            "            save_dir = Path('detection_results')",
            "            save_dir.mkdir(exist_ok=True)",
            "",
            "            # 生成文件名",
            "            timestamp = time.strftime('%Y%m%d_%H%M%S')",
            "            filename = f'detection_{timestamp}_{len(detections)}targets.png'",
            "            save_path = save_dir / filename",
            "",
            "            # 在图像上绘制检测框",
            "            if CV2_AVAILABLE:",
            "                result_image = image.copy()",
            "                for det in detections:",
            "                    x, y, w, h = det.bbox",
            "                    # 绘制边界框",
            "                    cv2.rectangle(result_image, (x, y), (x + w, y + h), (0, 255, 0), 2)",
            "                    # 绘制标签",
            "                    label = f'{det.class_name}: {det.confidence:.2f}'",
            "                    cv2.putText(result_image, label, (x, y - 10), ",
            "                               cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)",
            "",
            "                # 保存图像",
            "                cv2.imwrite(str(save_path), result_image)",
            "                logger.info(f'检测结果已保存: {save_path}')",
            "                return str(save_path)",
            "",
            "        except Exception as e:",
            "            logger.error(f'保存截图失败: {e}')",
            "            return None",
            "",
            "    def run_continuous_detection(self, max_detections: int = 10):",
            '        """运行连续检测"""',
            "        logger.info(f'开始连续检测，最多执行 {max_detections} 次')",
            "        logger.info(f'检测间隔: {EXPORTED_DETECTION_INTERVAL} 秒')",
            "",
            "        for i in range(max_detections):",
            "            logger.info(f'\\n=== 第 {i + 1}/{max_detections} 次检测 ===')",
            "",
            "            result = self.perform_detection()",
            "",
            "            if result['success']:",
            "                detections = result['detections']",
            "                if detections:",
            "                    logger.info(f'✅ 检测到 {len(detections)} 个目标:')",
            "                    for j, det in enumerate(detections, 1):",
            "                        logger.info(f'  {j}. {det.class_name} (置信度: {det.confidence:.3f})')",
            "                else:",
            "                    logger.info('ℹ️ 未检测到目标')",
            "            else:",
            "                logger.error(f'❌ 检测失败: {result.get(\"error\", \"未知错误\")}')",
            "",
            "            # 等待下次检测",
            "            if i < max_detections - 1:",
            "                time.sleep(EXPORTED_DETECTION_INTERVAL)",
            "",
            "        logger.info('\\n🎯 连续检测完成')",
            "",
            "# ==================== 主函数 ====================",
            "def main():",
            '    """主函数 - 演示完整的检测功能"""',
            "    print('🎯 YOLO检测器 - 完整功能版本')",
            "    print(f'📊 检测模式: {EXPORTED_DETECTION_MODE}')",
            "    print(f'📊 固化参数: 置信度={EXPORTED_CONFIDENCE_THRESHOLD}, NMS={EXPORTED_NMS_THRESHOLD}')",
            "    print(f'📊 模板匹配: {\"启用\" if EXPORTED_TEMPLATE_MATCHING_ENABLED else \"禁用\"}')",
            "    if EXPORTED_TEMPLATE_MATCHING_ENABLED:",
            "        print(f'📊 模板名称: {EXPORTED_TEMPLATE_NAME}')",
            "    print(f'📊 检测间隔: {EXPORTED_DETECTION_INTERVAL} 秒')",
            "    print()",
            "",
            "    try:",
            "        # 创建检测器实例",
            "        detector = GUIDetectorCopy()",
            "",
            "        # 执行单次检测演示",
            "        print('🔍 执行单次检测演示...')",
            "        result = detector.perform_detection()",
            "",
            "        if result['success']:",
            "            detections = result['detections']",
            "            print(f'✅ 单次检测完成，发现 {len(detections)} 个目标')",
            "            print(f'   处理时间: {result[\"processing_time\"]:.3f} 秒')",
            "            print(f'   图像尺寸: {result[\"image_shape\"]}')",
            "            if result.get('save_path'):",
            "                print(f'   保存路径: {result[\"save_path\"]}')",
            "        else:",
            "            print(f'❌ 单次检测失败: {result.get(\"error\", \"未知错误\")}')",
            "",
            "        # 询问是否进行连续检测",
            "        print('\\n是否进行连续检测演示？(y/n): ', end='')",
            "        user_input = input().strip().lower()",
            "",
            "        if user_input in ['y', 'yes', '是']:",
            "            detector.run_continuous_detection(max_detections=5)",
            "",
            "        print('\\n✅ 程序执行完成！')",
            "",
            "    except KeyboardInterrupt:",
            "        print('\\n⚠️ 用户中断程序')",
            "    except Exception as e:",
            "        logger.error(f'程序执行失败: {e}')",
            "        print(f'❌ 程序执行失败: {e}')",
            "",
            "if __name__ == '__main__':",
            "    main()",
            ""
        ])

        return '\n'.join(code_lines)
