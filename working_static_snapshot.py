#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
YOLO检测器 - GUI状态静态快照（可工作版本）
导出时间: 2025-07-27T12:02:01.307337
"""

from datetime import datetime

# 导出时刻的固化参数
EXPORTED_CONFIDENCE_THRESHOLD = 0.75
EXPORTED_NMS_THRESHOLD = 0.45
EXPORTED_TEMPLATE_NAME = '测试模板'
EXPORTED_DETECTION_INTERVAL = 2.0
EXPORTED_AUTO_DETECT = True
EXPORTED_TIMESTAMP = "2025-07-27T12:02:01.307337"

def main():
    """主函数"""
    print("🎯 YOLO检测器 - 静态快照版本（可工作版）")
    print("📅 导出时间: " + EXPORTED_TIMESTAMP)
    print("⚙️  固化参数:")
    print("   • 置信度阈值: " + str(EXPORTED_CONFIDENCE_THRESHOLD))
    print("   • NMS阈值: " + str(EXPORTED_NMS_THRESHOLD))
    print("   • 选择模板: " + str(EXPORTED_TEMPLATE_NAME))
    print("   • 检测间隔: " + str(EXPORTED_DETECTION_INTERVAL) + "秒")
    print("   • 自动检测: " + str(EXPORTED_AUTO_DETECT))
    print("\n✅ 静态快照代码运行成功！")

if __name__ == "__main__":
    main()
