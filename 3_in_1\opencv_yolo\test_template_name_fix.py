#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模板名称处理修复
"""

import sys
import os
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_template_name_cleaning():
    """测试模板名称清理逻辑"""
    print("🧪 测试模板名称清理逻辑...")
    
    test_cases = [
        "[通用] 本地磁盘",
        "[按钮] 登录按钮",
        "[图标] 菜单图标",
        "普通模板名称",
        "[分类] 复杂的[模板]名称",
    ]
    
    for template_name in test_cases:
        # 模拟清理逻辑
        clean_name = template_name
        if '] ' in clean_name:
            clean_name = clean_name.split('] ', 1)[1]
        
        print(f"  原始: '{template_name}' -> 清理后: '{clean_name}'")
    
    print("✅ 模板名称清理测试完成\n")

def test_template_file_search():
    """测试模板文件搜索逻辑"""
    print("🧪 测试模板文件搜索逻辑...")
    
    templates_dir = Path("templates")
    if not templates_dir.exists():
        print("⚠️ templates目录不存在")
        return
    
    # 测试搜索逻辑
    test_names = ["本地磁盘", "磁盘图标", "登录按钮"]
    
    for clean_name in test_names:
        print(f"🔍 搜索模板: '{clean_name}'")
        
        # 精确匹配
        exact_paths = [
            f"templates/{clean_name}.png",
            f"templates/{clean_name}.jpg",
            f"templates/{clean_name}.jpeg",
        ]
        
        found_exact = False
        for path in exact_paths:
            if Path(path).exists():
                print(f"  ✅ 精确匹配: {path}")
                found_exact = True
                break
        
        if not found_exact:
            # 模糊匹配
            print(f"  🔍 尝试模糊匹配...")
            found_fuzzy = False
            for file_path in templates_dir.glob("*.png"):
                if clean_name in file_path.stem:
                    print(f"  ✅ 模糊匹配: {file_path}")
                    found_fuzzy = True
                    break
            
            if not found_fuzzy:
                print(f"  ❌ 未找到匹配文件")
        
        print()
    
    print("✅ 模板文件搜索测试完成\n")

def test_template_data_structure():
    """测试模板数据结构"""
    print("🧪 测试模板数据结构...")
    
    # 模拟修复后的template_data结构
    original_name = "[通用] 本地磁盘"
    clean_name = original_name.split('] ', 1)[1] if '] ' in original_name else original_name
    
    template_data = {
        'name': clean_name,  # 使用清理后的名称
        'display_name': original_name,  # 保留原始显示名称
        'threshold': 0.8,
        'method': 'TM_CCOEFF_NORMED',
        'enabled': True,
        'path': f'templates/{clean_name}.png'
    }
    
    print("修复后的template_data结构:")
    for key, value in template_data.items():
        print(f"  {key}: {value}")
    
    # 验证代码生成时使用的名称
    template_name_for_code = template_data['name']  # 应该是清理后的名称
    print(f"\n代码生成使用的模板名称: '{template_name_for_code}'")
    print(f"是否包含分类前缀: {'是' if '] ' in template_name_for_code else '否'}")
    
    print("✅ 模板数据结构测试完成\n")

def main():
    """主函数"""
    print("🚀 开始测试模板名称处理修复...")
    print("=" * 50)
    
    test_template_name_cleaning()
    test_template_file_search()
    test_template_data_structure()
    
    print("=" * 50)
    print("✅ 所有测试完成")

if __name__ == "__main__":
    main()
