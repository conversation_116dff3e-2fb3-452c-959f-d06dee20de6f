#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试
"""

print("🧪 开始简单测试...")

try:
    import sys
    from pathlib import Path
    
    # 添加项目路径
    project_root = Path(__file__).parent / "3_in_1" / "opencv_yolo" / "src"
    sys.path.insert(0, str(project_root))
    
    print("✅ 路径设置成功")
    
    # 测试导入
    from yolo_opencv_detector.gui.dialogs.source_code_dialog import SourceCodeDialog
    print("✅ 源代码对话框导入成功")
    
    # 创建实例
    dialog = SourceCodeDialog()
    print("✅ 对话框实例创建成功")
    
    print("🎉 所有测试通过！")
    
except Exception as e:
    print(f"❌ 测试失败: {e}")
    import traceback
    traceback.print_exc()
