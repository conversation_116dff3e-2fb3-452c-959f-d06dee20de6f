#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模板核心功能测试（不依赖GUI）

测试新模板管理系统的核心功能，避免GUI依赖。

Created: 2025-07-27
Author: Augment Agent
"""

import sys
import os
import time
from pathlib import Path

# 添加项目路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_core_modules():
    """测试核心模块导入"""
    print("🧪 测试核心模块导入...")
    
    try:
        # 直接导入核心模块，避免GUI依赖
        from yolo_opencv_detector.core.template_models import (
            Template, TemplateCategory, TemplateStatus, TemplateMetadata
        )
        print("✅ 模板数据模型导入成功")
        
        from yolo_opencv_detector.core.template_validator import (
            TemplateValidator, clean_template_name, build_display_name
        )
        print("✅ 模板验证器导入成功")
        
        from yolo_opencv_detector.core.template_cache import TemplateCache
        print("✅ 模板缓存导入成功")
        
        from yolo_opencv_detector.core.template_repository import TemplateRepository
        print("✅ 模板存储导入成功")
        
        from yolo_opencv_detector.core.template_manager import TemplateManager
        print("✅ 模板管理器导入成功")
        
        print("✅ 所有核心模块导入成功\n")
        return True
        
    except Exception as e:
        print(f"❌ 核心模块导入失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_template_name_processing():
    """测试模板名称处理"""
    print("🧪 测试模板名称处理...")
    
    try:
        from yolo_opencv_detector.core.template_validator import clean_template_name, build_display_name
        from yolo_opencv_detector.core.template_models import TemplateCategory
        
        # 测试用例
        test_cases = [
            ("[通用] 本地磁盘", "本地磁盘"),
            ("[按钮] 登录按钮", "登录按钮"),
            ("[图标] 菜单图标", "菜单图标"),
            ("普通模板名称", "普通模板名称"),
            ("[分类] 复杂的[模板]名称", "复杂的[模板]名称"),
        ]
        
        print("名称清理测试:")
        for original, expected in test_cases:
            cleaned = clean_template_name(original)
            status = "✅" if cleaned == expected else "❌"
            print(f"  {status} '{original}' -> '{cleaned}' (期望: '{expected}')")
        
        print("\n显示名称构建测试:")
        for _, clean_name in test_cases[:3]:
            display_name = build_display_name(clean_name, TemplateCategory.GENERAL)
            print(f"  '{clean_name}' -> '{display_name}'")
        
        print("✅ 模板名称处理测试完成\n")
        return True
        
    except Exception as e:
        print(f"❌ 模板名称处理测试失败: {e}")
        return False


def test_template_data_model():
    """测试模板数据模型"""
    print("🧪 测试模板数据模型...")
    
    try:
        from yolo_opencv_detector.core.template_models import (
            Template, TemplateCategory, TemplateStatus, TemplateMetadata
        )
        
        # 创建模板对象
        template = Template(
            id="test-001",
            name="本地磁盘",
            display_name="[通用] 本地磁盘",
            description="测试模板",
            category=TemplateCategory.GENERAL,
            status=TemplateStatus.ACTIVE,
            threshold=0.8,
            method="TM_CCOEFF_NORMED"
        )
        
        print("✅ 模板对象创建成功")
        print(f"  ID: {template.id}")
        print(f"  清理后名称: {template.clean_name}")
        print(f"  完整显示名称: {template.full_display_name}")
        print(f"  是否有效: {template.is_valid}")
        
        # 测试序列化
        template_dict = template.to_dict()
        print("✅ 序列化成功")
        
        # 测试反序列化
        restored_template = Template.from_dict(template_dict)
        print("✅ 反序列化成功")
        
        # 验证数据一致性
        assert restored_template.id == template.id
        assert restored_template.name == template.name
        assert restored_template.category == template.category
        print("✅ 数据一致性验证通过")
        
        # 测试不可变性
        updated_template = template.update(description="新描述")
        assert template.description != updated_template.description
        print("✅ 不可变性验证通过")
        
        print("✅ 模板数据模型测试完成\n")
        return True
        
    except Exception as e:
        print(f"❌ 模板数据模型测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_template_cache():
    """测试模板缓存"""
    print("🧪 测试模板缓存...")
    
    try:
        from yolo_opencv_detector.core.template_cache import TemplateCache
        import numpy as np
        
        # 创建缓存
        cache = TemplateCache(image_cache_size=3, max_memory_mb=1)
        print("✅ 缓存创建成功")
        
        # 创建测试图像
        test_images = []
        for i in range(5):
            image = np.random.randint(0, 255, (50, 50, 3), dtype=np.uint8)
            test_images.append((f"test-{i:03d}", image))
        
        # 测试缓存添加
        for template_id, image in test_images:
            cache.image_cache.put_image(template_id, image, Path(f"{template_id}.png"))
        
        print(f"✅ 添加了 {len(test_images)} 个图像到缓存")
        
        # 检查缓存大小限制
        stats = cache.get_stats()
        cache_size = stats['image_cache']['cache_size']
        print(f"  实际缓存大小: {cache_size} (限制: 3)")
        assert cache_size <= 3, "缓存大小超出限制"
        
        # 测试缓存获取
        cached_image = cache.image_cache.get_image("test-004")
        if cached_image is not None:
            print("✅ 最新图像缓存获取成功")
        
        # 测试LRU机制
        old_image = cache.image_cache.get_image("test-000")
        if old_image is None:
            print("✅ LRU机制工作正常（旧图像被清理）")
        
        # 清空缓存
        cache.clear_all()
        final_stats = cache.get_stats()
        assert final_stats['image_cache']['cache_size'] == 0
        print("✅ 缓存清空成功")
        
        print("✅ 模板缓存测试完成\n")
        return True
        
    except Exception as e:
        print(f"❌ 模板缓存测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_template_repository():
    """测试模板存储"""
    print("🧪 测试模板存储...")
    
    try:
        from yolo_opencv_detector.core.template_repository import TemplateRepository
        from yolo_opencv_detector.core.template_models import Template, TemplateCategory, TemplateStatus
        
        # 创建临时存储目录
        test_storage = Path("test_storage")
        repo = TemplateRepository(test_storage, "json")
        print("✅ 存储仓库创建成功")
        
        # 创建测试模板
        template = Template(
            id="repo-test-001",
            name="存储测试模板",
            display_name="[通用] 存储测试模板",
            description="用于测试存储功能",
            category=TemplateCategory.GENERAL,
            status=TemplateStatus.ACTIVE
        )
        
        # 保存模板
        repo.save_template(template)
        print("✅ 模板保存成功")
        
        # 加载模板
        loaded_template = repo.load_template("repo-test-001")
        assert loaded_template.id == template.id
        assert loaded_template.name == template.name
        print("✅ 模板加载成功")
        
        # 加载所有模板
        all_templates = repo.load_all_templates()
        assert "repo-test-001" in all_templates
        print(f"✅ 加载所有模板成功，共 {len(all_templates)} 个")
        
        # 删除模板
        success = repo.delete_template("repo-test-001")
        assert success
        print("✅ 模板删除成功")
        
        # 验证删除
        remaining_templates = repo.load_all_templates()
        assert "repo-test-001" not in remaining_templates
        print("✅ 删除验证通过")
        
        # 清理测试目录
        import shutil
        if test_storage.exists():
            shutil.rmtree(test_storage)
        
        print("✅ 模板存储测试完成\n")
        return True
        
    except Exception as e:
        print(f"❌ 模板存储测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_integration():
    """集成测试"""
    print("🧪 集成测试...")
    
    try:
        from yolo_opencv_detector.core.template_manager import TemplateManager
        from yolo_opencv_detector.core.template_models import TemplateCategory
        
        # 创建管理器
        manager = TemplateManager("integration_test_storage", "json")
        print("✅ 模板管理器创建成功")
        
        # 测试统计信息
        stats = manager.get_stats()
        print(f"✅ 获取统计信息成功")
        print(f"  存储目录: {stats['manager_info']['storage_dir']}")
        print(f"  模板数量: {stats['storage']['template_count']}")
        
        # 测试按名称查找（应该返回None）
        template = manager.get_template_by_name("不存在的模板")
        assert template is None
        print("✅ 不存在模板查找测试通过")
        
        # 清理测试目录
        import shutil
        test_dir = Path("integration_test_storage")
        if test_dir.exists():
            shutil.rmtree(test_dir)
        
        print("✅ 集成测试完成\n")
        return True
        
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 开始测试模板管理系统核心功能...")
    print("=" * 60)
    
    tests = [
        test_core_modules,
        test_template_name_processing,
        test_template_data_model,
        test_template_cache,
        test_template_repository,
        test_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        if test_func():
            passed += 1
    
    print("=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！新模板管理系统核心功能正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")


if __name__ == "__main__":
    main()
