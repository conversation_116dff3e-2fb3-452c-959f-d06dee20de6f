#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中文文字渲染工具
解决OpenCV cv2.putText不支持中文字符的问题
作者: AI Assistant
创建时间: 2025-07-09
编码标准: UTF-8无BOM
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import os
import sys
from pathlib import Path
from typing import Tuple, Optional, Union
import logging

class ChineseTextRenderer:
    """中文文字渲染器"""
    
    def __init__(self):
        """初始化中文文字渲染器"""
        self.logger = logging.getLogger(__name__)
        self.font_cache = {}
        self._load_chinese_fonts()
    
    def _load_chinese_fonts(self):
        """加载中文字体"""
        # Windows系统中文字体路径
        windows_fonts = [
            "C:/Windows/Fonts/simhei.ttf",      # 黑体
            "C:/Windows/Fonts/simsun.ttc",      # 宋体
            "C:/Windows/Fonts/msyh.ttc",        # 微软雅黑
            "C:/Windows/Fonts/simkai.ttf",      # 楷体
            "C:/Windows/Fonts/simfang.ttf",     # 仿宋
        ]
        
        # Linux系统中文字体路径
        linux_fonts = [
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",
            "/usr/share/fonts/opentype/noto/NotoSansCJK-Regular.ttc",
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        ]
        
        # macOS系统中文字体路径
        macos_fonts = [
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/STHeiti Light.ttc",
            "/System/Library/Fonts/Hiragino Sans GB.ttc",
        ]
        
        # 根据操作系统选择字体路径
        if sys.platform.startswith('win'):
            font_paths = windows_fonts
        elif sys.platform.startswith('linux'):
            font_paths = linux_fonts
        elif sys.platform.startswith('darwin'):
            font_paths = macos_fonts
        else:
            font_paths = windows_fonts  # 默认使用Windows路径
        
        # 查找可用的字体
        self.available_fonts = []
        for font_path in font_paths:
            if os.path.exists(font_path):
                self.available_fonts.append(font_path)
                self.logger.debug(f"找到中文字体: {font_path}")
        
        if not self.available_fonts:
            self.logger.warning("未找到中文字体，将使用默认字体")
            self.available_fonts = [None]  # 使用默认字体
    
    def get_font(self, size: int = 16, font_index: int = 0) -> ImageFont.FreeTypeFont:
        """
        获取指定大小的字体
        
        Args:
            size: 字体大小
            font_index: 字体索引（0为首选字体）
            
        Returns:
            PIL字体对象
        """
        cache_key = (size, font_index)
        
        if cache_key in self.font_cache:
            return self.font_cache[cache_key]
        
        try:
            if font_index < len(self.available_fonts) and self.available_fonts[font_index]:
                font = ImageFont.truetype(self.available_fonts[font_index], size)
            else:
                font = ImageFont.load_default()
                self.logger.warning(f"使用默认字体，大小: {size}")
        except Exception as e:
            self.logger.error(f"加载字体失败: {e}")
            font = ImageFont.load_default()
        
        self.font_cache[cache_key] = font
        return font
    
    def put_chinese_text(self, 
                        image: np.ndarray,
                        text: str,
                        position: Tuple[int, int],
                        font_size: int = 16,
                        color: Tuple[int, int, int] = (0, 255, 0),
                        font_index: int = 0,
                        background_color: Optional[Tuple[int, int, int]] = None,
                        padding: int = 2) -> np.ndarray:
        """
        在OpenCV图像上绘制中文文字
        
        Args:
            image: OpenCV图像 (BGR格式)
            text: 要绘制的文字
            position: 文字位置 (x, y)
            font_size: 字体大小
            color: 文字颜色 (B, G, R)
            font_index: 字体索引
            background_color: 背景颜色，None表示透明
            padding: 背景边距
            
        Returns:
            绘制文字后的图像
        """
        if not text.strip():
            return image
        
        try:
            # 转换为PIL图像
            image_pil = Image.fromarray(cv2.cvtColor(image, cv2.COLOR_BGR2RGB))
            draw = ImageDraw.Draw(image_pil)
            
            # 获取字体
            font = self.get_font(font_size, font_index)
            
            # 计算文字尺寸
            bbox = draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 调整位置（PIL的坐标系统）
            x, y = position
            
            # 绘制背景（如果指定）
            if background_color is not None:
                bg_x1 = x - padding
                bg_y1 = y - padding
                bg_x2 = x + text_width + padding
                bg_y2 = y + text_height + padding
                
                # 转换颜色格式 (BGR -> RGB)
                bg_color_rgb = (background_color[2], background_color[1], background_color[0])
                draw.rectangle([bg_x1, bg_y1, bg_x2, bg_y2], fill=bg_color_rgb)
            
            # 绘制文字
            # 转换颜色格式 (BGR -> RGB)
            text_color_rgb = (color[2], color[1], color[0])
            draw.text((x, y), text, font=font, fill=text_color_rgb)
            
            # 转换回OpenCV格式
            result_image = cv2.cvtColor(np.array(image_pil), cv2.COLOR_RGB2BGR)
            
            return result_image
            
        except Exception as e:
            self.logger.error(f"绘制中文文字失败: {e}")
            # 如果失败，尝试使用OpenCV的英文渲染（可能显示为问号）
            cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX, 
                       font_size/20.0, color, 1, cv2.LINE_AA)
            return image
    
    def put_text_with_background(self,
                                image: np.ndarray,
                                text: str,
                                position: Tuple[int, int],
                                font_size: int = 16,
                                text_color: Tuple[int, int, int] = (255, 255, 255),
                                bg_color: Tuple[int, int, int] = (0, 255, 0),
                                alpha: float = 0.7,
                                padding: int = 5) -> np.ndarray:
        """
        绘制带半透明背景的中文文字
        
        Args:
            image: OpenCV图像
            text: 文字内容
            position: 位置
            font_size: 字体大小
            text_color: 文字颜色
            bg_color: 背景颜色
            alpha: 背景透明度
            padding: 边距
            
        Returns:
            绘制后的图像
        """
        if not text.strip():
            return image
        
        try:
            # 创建临时图像用于计算文字尺寸
            temp_img = Image.new('RGB', (1, 1))
            temp_draw = ImageDraw.Draw(temp_img)
            font = self.get_font(font_size)
            
            # 计算文字尺寸
            bbox = temp_draw.textbbox((0, 0), text, font=font)
            text_width = bbox[2] - bbox[0]
            text_height = bbox[3] - bbox[1]
            
            # 计算背景区域
            x, y = position
            bg_x1 = max(0, x - padding)
            bg_y1 = max(0, y - padding)
            bg_x2 = min(image.shape[1], x + text_width + padding)
            bg_y2 = min(image.shape[0], y + text_height + padding)
            
            # 创建半透明背景
            overlay = image.copy()
            cv2.rectangle(overlay, (bg_x1, bg_y1), (bg_x2, bg_y2), bg_color, -1)
            
            # 混合背景
            cv2.addWeighted(overlay, alpha, image, 1 - alpha, 0, image)
            
            # 绘制文字
            image = self.put_chinese_text(image, text, position, font_size, text_color)
            
            return image
            
        except Exception as e:
            self.logger.error(f"绘制带背景的中文文字失败: {e}")
            return image
    
    def is_chinese_text(self, text: str) -> bool:
        """
        检查文本是否包含中文字符
        
        Args:
            text: 待检查的文本
            
        Returns:
            是否包含中文字符
        """
        for char in text:
            if '\u4e00' <= char <= '\u9fff':  # 中文字符Unicode范围
                return True
        return False
    
    def smart_put_text(self,
                      image: np.ndarray,
                      text: str,
                      position: Tuple[int, int],
                      font_size: int = 16,
                      color: Tuple[int, int, int] = (0, 255, 0),
                      background: bool = True) -> np.ndarray:
        """
        智能文字渲染：自动检测中文并选择合适的渲染方法
        
        Args:
            image: OpenCV图像
            text: 文字内容
            position: 位置
            font_size: 字体大小
            color: 文字颜色
            background: 是否添加背景
            
        Returns:
            绘制后的图像
        """
        if not text.strip():
            return image
        
        # 检查是否包含中文
        if self.is_chinese_text(text):
            # 使用中文渲染
            if background:
                return self.put_text_with_background(
                    image, text, position, font_size, 
                    text_color=(255, 255, 255), bg_color=color
                )
            else:
                return self.put_chinese_text(image, text, position, font_size, color)
        else:
            # 使用OpenCV原生渲染（适用于英文和数字）
            cv2.putText(image, text, position, cv2.FONT_HERSHEY_SIMPLEX,
                       font_size/20.0, color, 1, cv2.LINE_AA)
            return image

# 全局实例
_chinese_renderer = None

def get_chinese_renderer() -> ChineseTextRenderer:
    """获取全局中文渲染器实例"""
    global _chinese_renderer
    if _chinese_renderer is None:
        _chinese_renderer = ChineseTextRenderer()
    return _chinese_renderer

def put_chinese_text(image: np.ndarray,
                    text: str,
                    position: Tuple[int, int],
                    font_size: int = 16,
                    color: Tuple[int, int, int] = (0, 255, 0),
                    background: bool = True) -> np.ndarray:
    """
    便捷函数：在图像上绘制中文文字
    
    Args:
        image: OpenCV图像
        text: 文字内容
        position: 位置
        font_size: 字体大小
        color: 颜色
        background: 是否添加背景
        
    Returns:
        绘制后的图像
    """
    renderer = get_chinese_renderer()
    return renderer.smart_put_text(image, text, position, font_size, color, background)
