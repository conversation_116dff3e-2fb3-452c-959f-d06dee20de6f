#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试模板匹配逻辑
作者: Cursor AI
创建时间: 2025-07-27
"""

import os
import re
from pathlib import Path

def load_template_image_by_name_simple(template_name):
    """简化版模板加载函数 - 只测试文件查找逻辑"""
    print(f"🔍 加载模板图像: {template_name}")

    # 清理模板名称，移除类别前缀
    clean_name = template_name
    if '] ' in template_name:
        clean_name = template_name.split('] ', 1)[1]

    print(f"   清理后的模板名称: {clean_name}")

    # 定义可能的模板目录
    template_dirs = [
        Path('templates'),
        Path('opencv_yolo/templates'),
        Path('3_in_1/opencv_yolo/templates'),
    ]

    # 支持的图像扩展名
    extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']

    # 1. 精确匹配
    print("🔍 尝试精确匹配...")
    for template_dir in template_dirs:
        if not template_dir.exists():
            continue
        for ext in extensions:
            exact_path = template_dir / f"{clean_name}{ext}"
            if exact_path.exists():
                print(f"   ✅ 精确匹配找到: {exact_path}")
                return str(exact_path)

    # 2. 模糊匹配（处理带时间戳的文件名）
    print("🔍 尝试模糊匹配...")
    for template_dir in template_dirs:
        if not template_dir.exists():
            continue
        
        try:
            for file_path in template_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in extensions:
                    # 检查文件名是否包含模板名称
                    if clean_name.lower() in file_path.stem.lower():
                        print(f"   ✅ 模糊匹配找到: {file_path}")
                        return str(file_path)
                    
                    # 检查去除特殊字符后的匹配
                    clean_template = re.sub(r'[^\w\u4e00-\u9fff]', '', clean_name)
                    clean_filename = re.sub(r'[^\w\u4e00-\u9fff]', '', file_path.stem)
                    if clean_template and clean_template.lower() in clean_filename.lower():
                        print(f"   ✅ 清理后匹配找到: {file_path}")
                        return str(file_path)
                        
        except Exception as e:
            print(f"   模糊匹配过程出错: {e}")
            continue

    print(f"❌ 未找到模板文件: {clean_name}")
    return None

def test_template_loading():
    """测试模板加载功能"""
    print("🧪 测试模板加载修复效果")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        "[通用] 本地磁盘",  # GUI显示的格式
        "本地磁盘",        # 清理后的格式
        "[通用] E",        # 另一个模板
        "E",              # 清理后的格式
        "磁盘图标",        # 中文模板
        "test_template",   # 英文模板
    ]
    
    print("\n🔍 开始测试模板加载...")
    success_count = 0
    
    for template_name in test_cases:
        print(f"\n📋 测试模板: {template_name}")
        print("-" * 30)
        
        try:
            result = load_template_image_by_name_simple(template_name)
            if result is not None:
                print(f"✅ 成功找到模板文件: {result}")
                success_count += 1
            else:
                print(f"❌ 未能找到模板: {template_name}")
        except Exception as e:
            print(f"❌ 查找过程出错: {e}")
    
    print(f"\n📊 测试结果: {success_count}/{len(test_cases)} 个模板找到")
    
    if success_count > 0:
        print("🎉 修复成功！模板文件查找逻辑工作正常")
        return True
    else:
        print("⚠️ 仍有问题，需要进一步调试")
        return False

def list_available_templates():
    """列出可用的模板文件"""
    print("\n📁 列出可用的模板文件:")
    print("-" * 30)
    
    template_dirs = [
        Path("templates"),
        Path("opencv_yolo/templates"),
        Path("3_in_1/opencv_yolo/templates"),
    ]
    
    extensions = ['.png', '.jpg', '.jpeg', '.bmp', '.tiff']
    
    for template_dir in template_dirs:
        if template_dir.exists():
            print(f"\n📂 目录: {template_dir}")
            files = []
            for file_path in template_dir.iterdir():
                if file_path.is_file() and file_path.suffix.lower() in extensions:
                    files.append(file_path.name)
            
            if files:
                for file_name in sorted(files):
                    print(f"   📄 {file_name}")
            else:
                print("   (空目录)")
        else:
            print(f"\n📂 目录: {template_dir} (不存在)")

if __name__ == "__main__":
    print("🚀 开始测试模板文件查找逻辑...")
    
    # 设置工作目录
    current_dir = Path(__file__).parent
    os.chdir(current_dir)
    print(f"📍 当前工作目录: {os.getcwd()}")
    
    # 列出可用模板
    list_available_templates()
    
    # 测试模板加载
    success = test_template_loading()
    
    if success:
        print("\n🎉 测试完成！模板文件查找逻辑已修复")
    else:
        print("\n⚠️ 测试完成，但仍需进一步调试")
